[package]
name = "whatsmeow-proto"
version = "0.1.0"
edition = "2021"
description = "Protocol buffer definitions and generated code for WhatsApp Web API"
license = "MIT"

[features]
default = []
serde = ["dep:serde", "dep:serde_json"]
debug = []
compression = ["dep:flate2"]

[dependencies]
prost = { workspace = true }
prost-types = { workspace = true }
bytes = { workspace = true }
thiserror = { workspace = true }
serde = { workspace = true, optional = true }
serde_json = { version = "1.0", optional = true }
base64 = { workspace = true }
flate2 = { version = "1.0", optional = true }
whatsmeow-types = { path = "../whatsmeow-types" }
once_cell = "1.21.3"

[build-dependencies]
prost-build = { workspace = true }
