//! Protocol buffer definitions and generated code for WhatsApp Web API
//!
//! This crate provides protocol buffer definitions and conversion utilities for the
//! WhatsApp Web API. It includes generated Rust code from protocol buffer definitions
//! and traits for seamless conversion between protocol buffer types and Rust types.
//!
//! # Features
//!
//! - Generated protocol buffer code from WhatsApp Web API definitions
//! - Conversion traits for seamless type conversion
//! - Comprehensive error handling
//! - Optional serde support for serialization
//!
//! # Examples
//!
//! ```rust
//! use whatsmeow_proto::{FromProto, ToProto, wa_proto};
//! use whatsmeow_types::MessageId;
//!
//! // Convert from protocol buffer to Rust type
//! let proto_key = wa_proto::MessageKey {
//!     remote_jid: Some("<EMAIL>".to_string()),
//!     from_me: Some(true),
//!     id: Some("message123".to_string()),
//!     participant: None,
//! };
//!
//! let message_id: MessageId = FromProto::from_proto(proto_key).unwrap();
//! ```

pub mod conversion;
pub mod error;
pub mod utils;

// Include generated protocol buffer code
pub mod wa_proto {
    #![allow(clippy::all)]
    #![allow(missing_docs)]
    include!(concat!(env!("OUT_DIR"), "/wa_proto.rs"));
}

// Re-export commonly used types
pub use conversion::{FromProto, ProtoConvert, ToProto};
pub use error::{ProtoError, ProtoResult};

// Re-export protocol buffer types for convenience
pub use wa_proto::*;
