//! Binary node parsing and serialization for WhatsApp Web protocol

pub mod error;
pub mod node;
pub mod parser;
pub mod serializer;
pub mod token;

#[cfg(test)]
mod tests;

#[cfg(test)]
mod edge_case_tests;

#[cfg(test)]
mod jid_tests;

#[cfg(test)]
mod protocol_tests;

#[cfg(test)]
mod binary_format_tests;

#[cfg(test)]
mod error_handling_tests;

#[cfg(test)]
mod performance_tests;

// Re-export commonly used types
pub use error::BinaryError;
pub use node::{AttributeValue, Node, NodeContent};
pub use parser::Parser;
pub use serializer::Serializer;
