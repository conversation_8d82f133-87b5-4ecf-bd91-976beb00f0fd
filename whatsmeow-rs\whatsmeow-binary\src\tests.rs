//! Tests for binary node parsing and serialization

use crate::node::{AttributeValue, Node, NodeContent};
use proptest::prelude::*;

#[test]
fn test_node_creation() {
    let node = Node::new("test");
    assert_eq!(node.tag, "test");
    assert!(node.attributes.is_empty());
    assert_eq!(node.content, NodeContent::None);
}

#[test]
fn test_node_with_text() {
    let node = Node::with_text("message", "Hello, World!");
    assert_eq!(node.tag, "message");
    assert_eq!(node.text(), Some(&"Hello, World!".to_string()));
}

#[test]
fn test_node_with_binary() {
    let data = vec![1, 2, 3, 4, 5];
    let node = Node::with_binary("data", data.clone());
    assert_eq!(node.tag, "data");
    assert_eq!(node.binary(), Some(&data));
}

#[test]
fn test_node_with_children() {
    let child1 = Node::with_text("child1", "text1");
    let child2 = Node::with_text("child2", "text2");
    let parent = Node::with_children("parent", vec![child1, child2]);

    assert_eq!(parent.tag, "parent");
    assert_eq!(parent.child_count(), 2);

    let children = parent.children().unwrap();
    assert_eq!(children[0].tag, "child1");
    assert_eq!(children[1].tag, "child2");
}

#[test]
fn test_node_attributes() {
    let mut node = Node::new("test")
        .with_attribute("id", "123")
        .with_attribute("type", "message")
        .with_attribute("count", 42i64)
        .with_attribute("enabled", true);

    assert_eq!(node.get_attribute_str("id"), Some("123"));
    assert_eq!(node.get_attribute_str("type"), Some("message"));
    assert_eq!(node.get_attribute_i64("count"), Some(42));
    assert_eq!(node.get_attribute_bool("enabled"), Some(true));

    // Test attribute modification
    node.set_attribute("new_attr", "new_value");
    assert_eq!(node.get_attribute_str("new_attr"), Some("new_value"));

    // Test attribute removal
    let removed = node.remove_attribute("id");
    assert!(removed.is_some());
    assert!(!node.has_attribute("id"));
}

#[test]
fn test_node_navigation() {
    let grandchild = Node::with_text("grandchild", "deep");
    let child = Node::with_children("child", vec![grandchild]);
    let parent = Node::with_children("parent", vec![child]);

    // Test get_child_by_tag
    let found_child = parent.get_child_by_tag("child");
    assert!(found_child.is_some());
    assert_eq!(found_child.unwrap().tag, "child");

    // Test get_nested_child
    let found_grandchild = parent.get_nested_child(&["child", "grandchild"]);
    assert!(found_grandchild.is_some());
    assert_eq!(found_grandchild.unwrap().tag, "grandchild");
    assert_eq!(found_grandchild.unwrap().text(), Some(&"deep".to_string()));
}

#[test]
fn test_attribute_value_conversions() {
    let string_val = AttributeValue::from("test");
    assert_eq!(string_val.as_str(), Some("test"));
    assert_eq!(string_val.as_string(), "test");

    let int_val = AttributeValue::from(42i64);
    assert_eq!(int_val.as_i64(), Some(42));
    assert_eq!(int_val.as_string(), "42");

    let bool_val = AttributeValue::from(true);
    assert_eq!(bool_val.as_bool(), Some(true));
    assert_eq!(bool_val.as_string(), "true");
}

#[test]
fn test_token_functions() {
    // Test single byte tokens
    assert_eq!(crate::token::get_single_token(1), Some("xmlstreamstart"));
    assert_eq!(crate::token::find_single_token("xmlstreamstart"), Some(1));

    // Test validation functions
    assert!(crate::token::validate_nibble("123-45.67"));
    assert!(!crate::token::validate_nibble("123abc"));

    assert!(crate::token::validate_hex("123ABC"));
    assert!(!crate::token::validate_hex("123abc")); // lowercase not allowed

    // Test packing/unpacking
    assert_eq!(crate::token::pack_nibble('5'), Some(5));
    assert_eq!(crate::token::pack_nibble('-'), Some(10));
    assert_eq!(crate::token::unpack_nibble(5), Some('5'));
    assert_eq!(crate::token::unpack_nibble(10), Some('-'));

    assert_eq!(crate::token::pack_hex('A'), Some(10));
    assert_eq!(crate::token::unpack_hex(10), Some('A'));
}

#[test]
fn test_simple_serialization_roundtrip() {
    let original = Node::with_text("message", "Hello")
        .with_attribute("id", "123")
        .with_attribute("type", "text");

    // Serialize
    let serialized = original.serialize().expect("Serialization failed");

    // Deserialize
    let deserialized = Node::parse(&serialized).expect("Deserialization failed");

    // Compare
    assert_eq!(original.tag, deserialized.tag);
    assert_eq!(original.text(), deserialized.text());

    // Note: Attribute comparison might need special handling due to type conversions
    assert_eq!(
        original.get_attribute_str("id"),
        deserialized.get_attribute_str("id")
    );
    assert_eq!(
        original.get_attribute_str("type"),
        deserialized.get_attribute_str("type")
    );
}

#[test]
fn test_empty_node_serialization() {
    let node = Node::new("empty");
    let serialized = node.serialize().expect("Serialization failed");
    let deserialized = Node::parse(&serialized).expect("Deserialization failed");

    assert_eq!(node.tag, deserialized.tag);
    assert_eq!(node.content, deserialized.content);
    assert!(deserialized.attributes.is_empty());
}

#[test]
fn test_binary_content_roundtrip() {
    let data = vec![0x00, 0x01, 0x02, 0xFF, 0xFE, 0xFD];
    let original = Node::with_binary("data", data.clone());

    let serialized = original.serialize().expect("Serialization failed");
    let deserialized = Node::parse(&serialized).expect("Deserialization failed");

    assert_eq!(original.tag, deserialized.tag);
    assert_eq!(original.binary(), deserialized.binary());
}

#[test]
fn test_error_handling() {
    // Test parsing empty data
    let result = Node::parse(&[]);
    assert!(result.is_err());

    // Test parsing invalid data
    let result = Node::parse(&[0xFF, 0xFF, 0xFF]);
    assert!(result.is_err());

    // Test serializing with invalid data should not panic
    let mut node = Node::new("test");
    node.set_attribute("key", "value");
    let result = node.serialize();
    // Should succeed for valid node
    assert!(result.is_ok());
}

// Property-based tests
proptest! {
    #[test]
    fn test_string_roundtrip(s in "\\PC*") {
        if !s.is_empty() && s.len() < 1000 {
            let original = Node::with_text("test", &s);
            if let Ok(serialized) = original.serialize() {
                if let Ok(deserialized) = Node::parse(&serialized) {
                    prop_assert_eq!(original.text(), deserialized.text());
                }
            }
        }
    }

    #[test]
    fn test_attribute_roundtrip(key in "\\w+", value in "\\PC*") {
        if !key.is_empty() && !value.is_empty() && key.len() < 100 && value.len() < 100 {
            let original = Node::new("test").with_attribute(key.clone(), value.clone());
            if let Ok(serialized) = original.serialize() {
                if let Ok(deserialized) = Node::parse(&serialized) {
                    prop_assert_eq!(
                        original.get_attribute_str(&key),
                        deserialized.get_attribute_str(&key)
                    );
                }
            }
        }
    }

    #[test]
    fn test_binary_roundtrip(data in prop::collection::vec(any::<u8>(), 1..1000)) {
        let original = Node::with_binary("data", data.clone());
        if let Ok(serialized) = original.serialize() {
            if let Ok(deserialized) = Node::parse(&serialized) {
                // The content might be interpreted as text if it's valid UTF-8
                // So we check that the actual data is preserved, regardless of type
                match &deserialized.content {
                    crate::node::NodeContent::Binary(bin_data) => {
                        prop_assert_eq!(Some(&data), Some(bin_data));
                    }
                    crate::node::NodeContent::Text(text_data) => {
                        // If it was interpreted as text, the original data should be valid UTF-8
                        if let Ok(expected_text) = String::from_utf8(data.clone()) {
                            prop_assert_eq!(expected_text, text_data.clone());
                        } else {
                            // If original data wasn't valid UTF-8, this shouldn't happen
                            prop_assert!(false, "Invalid UTF-8 data was interpreted as text");
                        }
                    }
                    _ => {
                        prop_assert!(false, "Binary content was not preserved");
                    }
                }
            }
        }
    }
}

#[test]
fn test_nested_nodes_roundtrip() {
    let leaf1 = Node::with_text("leaf1", "value1");
    let leaf2 = Node::with_text("leaf2", "value2");
    let branch = Node::with_children("branch", vec![leaf1, leaf2]);
    let root = Node::with_children("root", vec![branch]).with_attribute("version", "1.0");

    let serialized = root.serialize().expect("Serialization failed");
    let deserialized = Node::parse(&serialized).expect("Deserialization failed");

    assert_eq!(root.tag, deserialized.tag);
    assert_eq!(root.child_count(), deserialized.child_count());
    assert_eq!(
        root.get_attribute_str("version"),
        deserialized.get_attribute_str("version")
    );

    // Check nested structure
    let root_children = deserialized.children().unwrap();
    assert_eq!(root_children.len(), 1);
    assert_eq!(root_children[0].tag, "branch");

    let branch_children = root_children[0].children().unwrap();
    assert_eq!(branch_children.len(), 2);
    assert_eq!(branch_children[0].tag, "leaf1");
    assert_eq!(branch_children[1].tag, "leaf2");
    assert_eq!(branch_children[0].text(), Some(&"value1".to_string()));
    assert_eq!(branch_children[1].text(), Some(&"value2".to_string()));
}
