//! Storage trait definitions

use crate::error::StoreError;
use async_trait::async_trait;
use std::collections::HashMap;
use whatsmeow_types::{Device, Jid};

/// Device storage interface for managing device information
#[async_trait]
pub trait DeviceStore: Send + Sync {
    /// Get the current device information
    async fn get_device(&self) -> Result<Option<Device>, StoreError>;

    /// Store device information
    async fn put_device(&self, device: &Device) -> Result<(), StoreError>;

    /// Delete device information
    async fn delete_device(&self) -> Result<(), StoreError>;

    /// Check if device exists
    async fn has_device(&self) -> Result<bool, StoreError> {
        Ok(self.get_device().await?.is_some())
    }

    /// Get device JID if available
    async fn get_device_jid(&self) -> Result<Option<Jid>, StoreError> {
        Ok(self.get_device().await?.map(|d| d.jid))
    }
}

/// Session storage interface for managing Signal protocol sessions
#[async_trait]
pub trait SessionStore: Send + Sync {
    /// Get a session by address
    async fn get_session(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError>;

    /// Store a session
    async fn put_session(&self, address: &str, session: &[u8]) -> Result<(), StoreError>;

    /// Delete a specific session
    async fn delete_session(&self, address: &str) -> Result<(), StoreError>;

    /// Delete all sessions for a phone number
    async fn delete_all_sessions(&self, phone: &str) -> Result<(), StoreError>;

    /// Get all sessions for a phone number
    async fn get_sessions_for_phone(
        &self,
        phone: &str,
    ) -> Result<HashMap<String, Vec<u8>>, StoreError>;

    /// Check if a session exists
    async fn has_session(&self, address: &str) -> Result<bool, StoreError> {
        Ok(self.get_session(address).await?.is_some())
    }

    /// Get the count of sessions
    async fn get_session_count(&self) -> Result<usize, StoreError>;

    /// Clear all sessions
    async fn clear_all_sessions(&self) -> Result<(), StoreError>;
}

/// PreKey storage interface for managing one-time prekeys
#[async_trait]
pub trait PreKeyStore: Send + Sync {
    /// Get or generate prekeys up to the specified count
    async fn get_or_gen_prekeys(&self, count: u32) -> Result<Vec<PreKey>, StoreError>;

    /// Get a specific prekey by ID
    async fn get_prekey(&self, id: u32) -> Result<Option<PreKey>, StoreError>;

    /// Remove a prekey by ID
    async fn remove_prekey(&self, id: u32) -> Result<(), StoreError>;

    /// Mark prekeys as uploaded up to the specified ID
    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> Result<(), StoreError>;

    /// Get all available prekeys
    async fn get_all_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;

    /// Get unuploaded prekeys
    async fn get_unuploaded_prekeys(&self) -> Result<Vec<PreKey>, StoreError>;

    /// Get the count of available prekeys
    async fn get_prekey_count(&self) -> Result<usize, StoreError>;

    /// Remove multiple prekeys by IDs
    async fn remove_prekeys(&self, ids: &[u32]) -> Result<(), StoreError>;

    /// Clear all prekeys
    async fn clear_all_prekeys(&self) -> Result<(), StoreError>;

    /// Get the next available prekey ID
    async fn get_next_prekey_id(&self) -> Result<u32, StoreError>;
}

/// Sender key storage interface for group message encryption
#[async_trait]
pub trait SenderKeyStore: Send + Sync {
    /// Get a sender key for a specific group and sender
    async fn get_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
    ) -> Result<Option<Vec<u8>>, StoreError>;

    /// Store a sender key for a specific group and sender
    async fn put_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
        key: &[u8],
    ) -> Result<(), StoreError>;

    /// Delete a sender key for a specific group and sender
    async fn delete_sender_key(&self, group_id: &str, sender_id: &str) -> Result<(), StoreError>;

    /// Get all sender keys for a group
    async fn get_group_sender_keys(
        &self,
        group_id: &str,
    ) -> Result<HashMap<String, Vec<u8>>, StoreError>;

    /// Delete all sender keys for a group
    async fn delete_group_sender_keys(&self, group_id: &str) -> Result<(), StoreError>;

    /// Check if a sender key exists
    async fn has_sender_key(&self, group_id: &str, sender_id: &str) -> Result<bool, StoreError> {
        Ok(self.get_sender_key(group_id, sender_id).await?.is_some())
    }

    /// Clear all sender keys
    async fn clear_all_sender_keys(&self) -> Result<(), StoreError>;
}

/// Identity key storage interface for managing identity keys
#[async_trait]
pub trait IdentityStore: Send + Sync {
    /// Get an identity key for an address
    async fn get_identity_key(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError>;

    /// Store an identity key for an address
    async fn put_identity_key(&self, address: &str, key: &[u8]) -> Result<(), StoreError>;

    /// Check if an identity key is trusted
    async fn is_trusted_identity(&self, address: &str, key: &[u8]) -> Result<bool, StoreError>;

    /// Delete an identity key
    async fn delete_identity_key(&self, address: &str) -> Result<(), StoreError>;

    /// Get all identity keys
    async fn get_all_identity_keys(&self) -> Result<HashMap<String, Vec<u8>>, StoreError>;

    /// Mark an identity key as trusted
    async fn mark_identity_trusted(&self, address: &str, key: &[u8]) -> Result<(), StoreError>;

    /// Mark an identity key as untrusted
    async fn mark_identity_untrusted(&self, address: &str) -> Result<(), StoreError>;

    /// Check if an identity key exists
    async fn has_identity_key(&self, address: &str) -> Result<bool, StoreError> {
        Ok(self.get_identity_key(address).await?.is_some())
    }

    /// Clear all identity keys
    async fn clear_all_identity_keys(&self) -> Result<(), StoreError>;
}

/// PreKey data structure for one-time prekeys
#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct PreKey {
    pub id: u32,
    pub key_pair: Vec<u8>, // Serialized key pair
    pub uploaded: bool,
    pub created_at: std::time::SystemTime,
}

impl PreKey {
    /// Create a new PreKey with the given ID and key pair
    pub fn new(id: u32, key_pair: Vec<u8>) -> Self {
        Self {
            id,
            key_pair,
            uploaded: false,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// Create a new PreKey that's already marked as uploaded
    pub fn new_uploaded(id: u32, key_pair: Vec<u8>) -> Self {
        Self {
            id,
            key_pair,
            uploaded: true,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// Mark this prekey as uploaded
    pub fn mark_uploaded(&mut self) {
        self.uploaded = true;
    }

    /// Check if this prekey is uploaded
    pub fn is_uploaded(&self) -> bool {
        self.uploaded
    }

    /// Get the age of this prekey
    pub fn age(&self) -> std::time::Duration {
        self.created_at.elapsed().unwrap_or_default()
    }
}

/// Signed PreKey data structure
#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct SignedPreKey {
    pub id: u32,
    pub key_pair: Vec<u8>, // Serialized key pair
    pub signature: Vec<u8>,
    pub created_at: std::time::SystemTime,
}

impl SignedPreKey {
    /// Create a new SignedPreKey
    pub fn new(id: u32, key_pair: Vec<u8>, signature: Vec<u8>) -> Self {
        Self {
            id,
            key_pair,
            signature,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// Get the age of this signed prekey
    pub fn age(&self) -> std::time::Duration {
        self.created_at.elapsed().unwrap_or_default()
    }
}

/// Storage statistics for monitoring and debugging
#[derive(Debug, Clone, Default, serde::Serialize, serde::Deserialize)]
pub struct StorageStats {
    pub device_count: usize,
    pub session_count: usize,
    pub prekey_count: usize,
    pub sender_key_count: usize,
    pub identity_key_count: usize,
    pub total_size_bytes: u64,
}

impl StorageStats {
    /// Create new empty storage stats
    pub fn new() -> Self {
        Self::default()
    }

    /// Check if storage is empty
    pub fn is_empty(&self) -> bool {
        self.device_count == 0
            && self.session_count == 0
            && self.prekey_count == 0
            && self.sender_key_count == 0
            && self.identity_key_count == 0
    }
}

/// Combined storage trait that includes all storage interfaces
#[async_trait]
pub trait Store:
    DeviceStore + SessionStore + PreKeyStore + SenderKeyStore + IdentityStore + Send + Sync
{
    /// Get storage statistics
    async fn get_stats(&self) -> Result<StorageStats, StoreError>;

    /// Clear all data from storage
    async fn clear_all(&self) -> Result<(), StoreError>;

    /// Backup storage to bytes
    async fn backup(&self) -> Result<Vec<u8>, StoreError>;

    /// Restore storage from bytes
    async fn restore(&self, data: &[u8]) -> Result<(), StoreError>;

    /// Validate storage integrity
    async fn validate(&self) -> Result<Vec<String>, StoreError>;
}
