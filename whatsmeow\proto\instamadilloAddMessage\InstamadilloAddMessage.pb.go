// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloAddMessage/InstamadilloAddMessage.proto

package instamadilloAddMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	instamadilloCoreTypeActionLog "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeActionLog"
	instamadilloCoreTypeAdminMessage "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeAdminMessage"
	instamadilloCoreTypeCollection "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeCollection"
	instamadilloCoreTypeLink "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeLink"
	instamadilloCoreTypeMedia "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeMedia"
	instamadilloCoreTypeText "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeText"
	instamadilloXmaContentRef "go.mau.fi/whatsmeow/proto/instamadilloXmaContentRef"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Placeholder_Type int32

const (
	Placeholder_PLACEHOLDER_TYPE_NONE                          Placeholder_Type = 0
	Placeholder_PLACEHOLDER_TYPE_DECRYPTION_FAILURE            Placeholder_Type = 1
	Placeholder_PLACEHOLDER_TYPE_NOT_SUPPORTED_NEED_UPDATE     Placeholder_Type = 2
	Placeholder_PLACEHOLDER_TYPE_DEVICE_UNAVAILABLE            Placeholder_Type = 3
	Placeholder_PLACEHOLDER_TYPE_NOT_SUPPORTED_NOT_RECOVERABLE Placeholder_Type = 4
)

// Enum value maps for Placeholder_Type.
var (
	Placeholder_Type_name = map[int32]string{
		0: "PLACEHOLDER_TYPE_NONE",
		1: "PLACEHOLDER_TYPE_DECRYPTION_FAILURE",
		2: "PLACEHOLDER_TYPE_NOT_SUPPORTED_NEED_UPDATE",
		3: "PLACEHOLDER_TYPE_DEVICE_UNAVAILABLE",
		4: "PLACEHOLDER_TYPE_NOT_SUPPORTED_NOT_RECOVERABLE",
	}
	Placeholder_Type_value = map[string]int32{
		"PLACEHOLDER_TYPE_NONE":                          0,
		"PLACEHOLDER_TYPE_DECRYPTION_FAILURE":            1,
		"PLACEHOLDER_TYPE_NOT_SUPPORTED_NEED_UPDATE":     2,
		"PLACEHOLDER_TYPE_DEVICE_UNAVAILABLE":            3,
		"PLACEHOLDER_TYPE_NOT_SUPPORTED_NOT_RECOVERABLE": 4,
	}
)

func (x Placeholder_Type) Enum() *Placeholder_Type {
	p := new(Placeholder_Type)
	*p = x
	return p
}

func (x Placeholder_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Placeholder_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_enumTypes[0].Descriptor()
}

func (Placeholder_Type) Type() protoreflect.EnumType {
	return &file_instamadilloAddMessage_InstamadilloAddMessage_proto_enumTypes[0]
}

func (x Placeholder_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Placeholder_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Placeholder_Type(num)
	return nil
}

// Deprecated: Use Placeholder_Type.Descriptor instead.
func (Placeholder_Type) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{10, 0}
}

type AddMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *AddMessageContent     `protobuf:"bytes,1,opt,name=content" json:"content,omitempty"`
	Metadata      *AddMessageMetadata    `protobuf:"bytes,2,opt,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddMessagePayload) Reset() {
	*x = AddMessagePayload{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMessagePayload) ProtoMessage() {}

func (x *AddMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMessagePayload.ProtoReflect.Descriptor instead.
func (*AddMessagePayload) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{0}
}

func (x *AddMessagePayload) GetContent() *AddMessageContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *AddMessagePayload) GetMetadata() *AddMessageMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type AddMessageContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to AddMessageContent:
	//
	//	*AddMessageContent_Text
	//	*AddMessageContent_Like
	//	*AddMessageContent_Link
	//	*AddMessageContent_ReceiverFetchXma
	//	*AddMessageContent_Media
	//	*AddMessageContent_Placeholder
	//	*AddMessageContent_Collection
	//	*AddMessageContent_AdminMessage
	//	*AddMessageContent_ActionLog
	AddMessageContent isAddMessageContent_AddMessageContent `protobuf_oneof:"addMessageContent"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AddMessageContent) Reset() {
	*x = AddMessageContent{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMessageContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMessageContent) ProtoMessage() {}

func (x *AddMessageContent) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMessageContent.ProtoReflect.Descriptor instead.
func (*AddMessageContent) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{1}
}

func (x *AddMessageContent) GetAddMessageContent() isAddMessageContent_AddMessageContent {
	if x != nil {
		return x.AddMessageContent
	}
	return nil
}

func (x *AddMessageContent) GetText() *instamadilloCoreTypeText.Text {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Text); ok {
			return x.Text
		}
	}
	return nil
}

func (x *AddMessageContent) GetLike() *Like {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Like); ok {
			return x.Like
		}
	}
	return nil
}

func (x *AddMessageContent) GetLink() *instamadilloCoreTypeLink.Link {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Link); ok {
			return x.Link
		}
	}
	return nil
}

func (x *AddMessageContent) GetReceiverFetchXma() *ReceiverFetchXma {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_ReceiverFetchXma); ok {
			return x.ReceiverFetchXma
		}
	}
	return nil
}

func (x *AddMessageContent) GetMedia() *instamadilloCoreTypeMedia.Media {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Media); ok {
			return x.Media
		}
	}
	return nil
}

func (x *AddMessageContent) GetPlaceholder() *Placeholder {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Placeholder); ok {
			return x.Placeholder
		}
	}
	return nil
}

func (x *AddMessageContent) GetCollection() *instamadilloCoreTypeCollection.Collection {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_Collection); ok {
			return x.Collection
		}
	}
	return nil
}

func (x *AddMessageContent) GetAdminMessage() *instamadilloCoreTypeAdminMessage.AdminMessage {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_AdminMessage); ok {
			return x.AdminMessage
		}
	}
	return nil
}

func (x *AddMessageContent) GetActionLog() *instamadilloCoreTypeActionLog.ActionLog {
	if x != nil {
		if x, ok := x.AddMessageContent.(*AddMessageContent_ActionLog); ok {
			return x.ActionLog
		}
	}
	return nil
}

type isAddMessageContent_AddMessageContent interface {
	isAddMessageContent_AddMessageContent()
}

type AddMessageContent_Text struct {
	Text *instamadilloCoreTypeText.Text `protobuf:"bytes,1,opt,name=text,oneof"`
}

type AddMessageContent_Like struct {
	Like *Like `protobuf:"bytes,2,opt,name=like,oneof"`
}

type AddMessageContent_Link struct {
	Link *instamadilloCoreTypeLink.Link `protobuf:"bytes,3,opt,name=link,oneof"`
}

type AddMessageContent_ReceiverFetchXma struct {
	ReceiverFetchXma *ReceiverFetchXma `protobuf:"bytes,4,opt,name=receiverFetchXma,oneof"`
}

type AddMessageContent_Media struct {
	Media *instamadilloCoreTypeMedia.Media `protobuf:"bytes,5,opt,name=media,oneof"`
}

type AddMessageContent_Placeholder struct {
	Placeholder *Placeholder `protobuf:"bytes,6,opt,name=placeholder,oneof"`
}

type AddMessageContent_Collection struct {
	Collection *instamadilloCoreTypeCollection.Collection `protobuf:"bytes,7,opt,name=collection,oneof"`
}

type AddMessageContent_AdminMessage struct {
	AdminMessage *instamadilloCoreTypeAdminMessage.AdminMessage `protobuf:"bytes,8,opt,name=adminMessage,oneof"`
}

type AddMessageContent_ActionLog struct {
	ActionLog *instamadilloCoreTypeActionLog.ActionLog `protobuf:"bytes,9,opt,name=actionLog,oneof"`
}

func (*AddMessageContent_Text) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_Like) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_Link) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_ReceiverFetchXma) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_Media) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_Placeholder) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_Collection) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_AdminMessage) isAddMessageContent_AddMessageContent() {}

func (*AddMessageContent_ActionLog) isAddMessageContent_AddMessageContent() {}

type AddMessageMetadata struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	SendSilently       *bool                  `protobuf:"varint,1,opt,name=sendSilently" json:"sendSilently,omitempty"`
	PrivateReplyInfo   *PrivateReplyInfo      `protobuf:"bytes,2,opt,name=privateReplyInfo" json:"privateReplyInfo,omitempty"`
	RepliedToMessage   *RepliedToMessage      `protobuf:"bytes,3,opt,name=repliedToMessage" json:"repliedToMessage,omitempty"`
	ForwardingParams   *ForwardingParams      `protobuf:"bytes,4,opt,name=forwardingParams" json:"forwardingParams,omitempty"`
	EphemeralityParams *EphemeralityParams    `protobuf:"bytes,5,opt,name=ephemeralityParams" json:"ephemeralityParams,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AddMessageMetadata) Reset() {
	*x = AddMessageMetadata{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMessageMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMessageMetadata) ProtoMessage() {}

func (x *AddMessageMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMessageMetadata.ProtoReflect.Descriptor instead.
func (*AddMessageMetadata) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{2}
}

func (x *AddMessageMetadata) GetSendSilently() bool {
	if x != nil && x.SendSilently != nil {
		return *x.SendSilently
	}
	return false
}

func (x *AddMessageMetadata) GetPrivateReplyInfo() *PrivateReplyInfo {
	if x != nil {
		return x.PrivateReplyInfo
	}
	return nil
}

func (x *AddMessageMetadata) GetRepliedToMessage() *RepliedToMessage {
	if x != nil {
		return x.RepliedToMessage
	}
	return nil
}

func (x *AddMessageMetadata) GetForwardingParams() *ForwardingParams {
	if x != nil {
		return x.ForwardingParams
	}
	return nil
}

func (x *AddMessageMetadata) GetEphemeralityParams() *EphemeralityParams {
	if x != nil {
		return x.EphemeralityParams
	}
	return nil
}

type RepliedToMessage struct {
	state                            protoimpl.MessageState           `protogen:"open.v1"`
	RepliedToMessageOtid             *string                          `protobuf:"bytes,1,opt,name=repliedToMessageOtid" json:"repliedToMessageOtid,omitempty"`
	RepliedToMessageWaServerTimeSec  *string                          `protobuf:"bytes,2,opt,name=repliedToMessageWaServerTimeSec" json:"repliedToMessageWaServerTimeSec,omitempty"`
	RepliedToMessageCollectionItemID *string                          `protobuf:"bytes,3,opt,name=repliedToMessageCollectionItemID" json:"repliedToMessageCollectionItemID,omitempty"`
	OmMicroSecTS                     *OpenMessageMicroSecondTimestamp `protobuf:"bytes,4,opt,name=omMicroSecTS" json:"omMicroSecTS,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *RepliedToMessage) Reset() {
	*x = RepliedToMessage{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RepliedToMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepliedToMessage) ProtoMessage() {}

func (x *RepliedToMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepliedToMessage.ProtoReflect.Descriptor instead.
func (*RepliedToMessage) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{3}
}

func (x *RepliedToMessage) GetRepliedToMessageOtid() string {
	if x != nil && x.RepliedToMessageOtid != nil {
		return *x.RepliedToMessageOtid
	}
	return ""
}

func (x *RepliedToMessage) GetRepliedToMessageWaServerTimeSec() string {
	if x != nil && x.RepliedToMessageWaServerTimeSec != nil {
		return *x.RepliedToMessageWaServerTimeSec
	}
	return ""
}

func (x *RepliedToMessage) GetRepliedToMessageCollectionItemID() string {
	if x != nil && x.RepliedToMessageCollectionItemID != nil {
		return *x.RepliedToMessageCollectionItemID
	}
	return ""
}

func (x *RepliedToMessage) GetOmMicroSecTS() *OpenMessageMicroSecondTimestamp {
	if x != nil {
		return x.OmMicroSecTS
	}
	return nil
}

type OpenMessageMicroSecondTimestamp struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TimestampMS      *int64                 `protobuf:"varint,1,opt,name=timestampMS" json:"timestampMS,omitempty"`
	MicroSecondsBits *int32                 `protobuf:"varint,2,opt,name=microSecondsBits" json:"microSecondsBits,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *OpenMessageMicroSecondTimestamp) Reset() {
	*x = OpenMessageMicroSecondTimestamp{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenMessageMicroSecondTimestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenMessageMicroSecondTimestamp) ProtoMessage() {}

func (x *OpenMessageMicroSecondTimestamp) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenMessageMicroSecondTimestamp.ProtoReflect.Descriptor instead.
func (*OpenMessageMicroSecondTimestamp) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{4}
}

func (x *OpenMessageMicroSecondTimestamp) GetTimestampMS() int64 {
	if x != nil && x.TimestampMS != nil {
		return *x.TimestampMS
	}
	return 0
}

func (x *OpenMessageMicroSecondTimestamp) GetMicroSecondsBits() int32 {
	if x != nil && x.MicroSecondsBits != nil {
		return *x.MicroSecondsBits
	}
	return 0
}

type PrivateReplyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentID     *string                `protobuf:"bytes,1,opt,name=commentID" json:"commentID,omitempty"`
	PostLink      *string                `protobuf:"bytes,2,opt,name=postLink" json:"postLink,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrivateReplyInfo) Reset() {
	*x = PrivateReplyInfo{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateReplyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateReplyInfo) ProtoMessage() {}

func (x *PrivateReplyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateReplyInfo.ProtoReflect.Descriptor instead.
func (*PrivateReplyInfo) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{5}
}

func (x *PrivateReplyInfo) GetCommentID() string {
	if x != nil && x.CommentID != nil {
		return *x.CommentID
	}
	return ""
}

func (x *PrivateReplyInfo) GetPostLink() string {
	if x != nil && x.PostLink != nil {
		return *x.PostLink
	}
	return ""
}

type ForwardingParams struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ForwardedThreadID *string                `protobuf:"bytes,1,opt,name=forwardedThreadID" json:"forwardedThreadID,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ForwardingParams) Reset() {
	*x = ForwardingParams{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardingParams) ProtoMessage() {}

func (x *ForwardingParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardingParams.ProtoReflect.Descriptor instead.
func (*ForwardingParams) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{6}
}

func (x *ForwardingParams) GetForwardedThreadID() string {
	if x != nil && x.ForwardedThreadID != nil {
		return *x.ForwardedThreadID
	}
	return ""
}

type EphemeralityParams struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	EphemeralDurationSec *int64                 `protobuf:"varint,1,opt,name=ephemeralDurationSec" json:"ephemeralDurationSec,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *EphemeralityParams) Reset() {
	*x = EphemeralityParams{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EphemeralityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EphemeralityParams) ProtoMessage() {}

func (x *EphemeralityParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EphemeralityParams.ProtoReflect.Descriptor instead.
func (*EphemeralityParams) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{7}
}

func (x *EphemeralityParams) GetEphemeralDurationSec() int64 {
	if x != nil && x.EphemeralDurationSec != nil {
		return *x.EphemeralDurationSec
	}
	return 0
}

type Like struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Like) Reset() {
	*x = Like{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Like) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Like) ProtoMessage() {}

func (x *Like) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Like.ProtoReflect.Descriptor instead.
func (*Like) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{8}
}

type ReceiverFetchXma struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	ContentRef    *string                                  `protobuf:"bytes,1,opt,name=contentRef" json:"contentRef,omitempty"`
	Text          *string                                  `protobuf:"bytes,2,opt,name=text" json:"text,omitempty"`
	Media         *instamadilloCoreTypeMedia.Media         `protobuf:"bytes,3,opt,name=media" json:"media,omitempty"`
	XmaContentRef *instamadilloXmaContentRef.XmaContentRef `protobuf:"bytes,4,opt,name=xmaContentRef" json:"xmaContentRef,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXma) Reset() {
	*x = ReceiverFetchXma{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXma) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXma) ProtoMessage() {}

func (x *ReceiverFetchXma) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXma.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXma) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{9}
}

func (x *ReceiverFetchXma) GetContentRef() string {
	if x != nil && x.ContentRef != nil {
		return *x.ContentRef
	}
	return ""
}

func (x *ReceiverFetchXma) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

func (x *ReceiverFetchXma) GetMedia() *instamadilloCoreTypeMedia.Media {
	if x != nil {
		return x.Media
	}
	return nil
}

func (x *ReceiverFetchXma) GetXmaContentRef() *instamadilloXmaContentRef.XmaContentRef {
	if x != nil {
		return x.XmaContentRef
	}
	return nil
}

type Placeholder struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PlaceholderType *Placeholder_Type      `protobuf:"varint,1,opt,name=placeholderType,enum=InstamadilloAddMessage.Placeholder_Type" json:"placeholderType,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Placeholder) Reset() {
	*x = Placeholder{}
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Placeholder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Placeholder) ProtoMessage() {}

func (x *Placeholder) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Placeholder.ProtoReflect.Descriptor instead.
func (*Placeholder) Descriptor() ([]byte, []int) {
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP(), []int{10}
}

func (x *Placeholder) GetPlaceholderType() Placeholder_Type {
	if x != nil && x.PlaceholderType != nil {
		return *x.PlaceholderType
	}
	return Placeholder_PLACEHOLDER_TYPE_NONE
}

var File_instamadilloAddMessage_InstamadilloAddMessage_proto protoreflect.FileDescriptor

const file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDesc = "" +
	"\n" +
	"3instamadilloAddMessage/InstamadilloAddMessage.proto\x12\x16InstamadilloAddMessage\x1aAinstamadilloCoreTypeActionLog/InstamadilloCoreTypeActionLog.proto\x1aGinstamadilloCoreTypeAdminMessage/InstamadilloCoreTypeAdminMessage.proto\x1aCinstamadilloCoreTypeCollection/InstamadilloCoreTypeCollection.proto\x1a7instamadilloCoreTypeLink/InstamadilloCoreTypeLink.proto\x1a9instamadilloCoreTypeMedia/InstamadilloCoreTypeMedia.proto\x1a7instamadilloCoreTypeText/InstamadilloCoreTypeText.proto\x1a9instamadilloXmaContentRef/InstamadilloXmaContentRef.proto\"\xa0\x01\n" +
	"\x11AddMessagePayload\x12C\n" +
	"\acontent\x18\x01 \x01(\v2).InstamadilloAddMessage.AddMessageContentR\acontent\x12F\n" +
	"\bmetadata\x18\x02 \x01(\v2*.InstamadilloAddMessage.AddMessageMetadataR\bmetadata\"\x91\x05\n" +
	"\x11AddMessageContent\x124\n" +
	"\x04text\x18\x01 \x01(\v2\x1e.InstamadilloCoreTypeText.TextH\x00R\x04text\x122\n" +
	"\x04like\x18\x02 \x01(\v2\x1c.InstamadilloAddMessage.LikeH\x00R\x04like\x124\n" +
	"\x04link\x18\x03 \x01(\v2\x1e.InstamadilloCoreTypeLink.LinkH\x00R\x04link\x12V\n" +
	"\x10receiverFetchXma\x18\x04 \x01(\v2(.InstamadilloAddMessage.ReceiverFetchXmaH\x00R\x10receiverFetchXma\x128\n" +
	"\x05media\x18\x05 \x01(\v2 .InstamadilloCoreTypeMedia.MediaH\x00R\x05media\x12G\n" +
	"\vplaceholder\x18\x06 \x01(\v2#.InstamadilloAddMessage.PlaceholderH\x00R\vplaceholder\x12L\n" +
	"\n" +
	"collection\x18\a \x01(\v2*.InstamadilloCoreTypeCollection.CollectionH\x00R\n" +
	"collection\x12T\n" +
	"\fadminMessage\x18\b \x01(\v2..InstamadilloCoreTypeAdminMessage.AdminMessageH\x00R\fadminMessage\x12H\n" +
	"\tactionLog\x18\t \x01(\v2(.InstamadilloCoreTypeActionLog.ActionLogH\x00R\tactionLogB\x13\n" +
	"\x11addMessageContent\"\x96\x03\n" +
	"\x12AddMessageMetadata\x12\"\n" +
	"\fsendSilently\x18\x01 \x01(\bR\fsendSilently\x12T\n" +
	"\x10privateReplyInfo\x18\x02 \x01(\v2(.InstamadilloAddMessage.PrivateReplyInfoR\x10privateReplyInfo\x12T\n" +
	"\x10repliedToMessage\x18\x03 \x01(\v2(.InstamadilloAddMessage.RepliedToMessageR\x10repliedToMessage\x12T\n" +
	"\x10forwardingParams\x18\x04 \x01(\v2(.InstamadilloAddMessage.ForwardingParamsR\x10forwardingParams\x12Z\n" +
	"\x12ephemeralityParams\x18\x05 \x01(\v2*.InstamadilloAddMessage.EphemeralityParamsR\x12ephemeralityParams\"\xb9\x02\n" +
	"\x10RepliedToMessage\x122\n" +
	"\x14repliedToMessageOtid\x18\x01 \x01(\tR\x14repliedToMessageOtid\x12H\n" +
	"\x1frepliedToMessageWaServerTimeSec\x18\x02 \x01(\tR\x1frepliedToMessageWaServerTimeSec\x12J\n" +
	" repliedToMessageCollectionItemID\x18\x03 \x01(\tR repliedToMessageCollectionItemID\x12[\n" +
	"\fomMicroSecTS\x18\x04 \x01(\v27.InstamadilloAddMessage.OpenMessageMicroSecondTimestampR\fomMicroSecTS\"o\n" +
	"\x1fOpenMessageMicroSecondTimestamp\x12 \n" +
	"\vtimestampMS\x18\x01 \x01(\x03R\vtimestampMS\x12*\n" +
	"\x10microSecondsBits\x18\x02 \x01(\x05R\x10microSecondsBits\"L\n" +
	"\x10PrivateReplyInfo\x12\x1c\n" +
	"\tcommentID\x18\x01 \x01(\tR\tcommentID\x12\x1a\n" +
	"\bpostLink\x18\x02 \x01(\tR\bpostLink\"@\n" +
	"\x10ForwardingParams\x12,\n" +
	"\x11forwardedThreadID\x18\x01 \x01(\tR\x11forwardedThreadID\"H\n" +
	"\x12EphemeralityParams\x122\n" +
	"\x14ephemeralDurationSec\x18\x01 \x01(\x03R\x14ephemeralDurationSec\"\x06\n" +
	"\x04Like\"\xce\x01\n" +
	"\x10ReceiverFetchXma\x12\x1e\n" +
	"\n" +
	"contentRef\x18\x01 \x01(\tR\n" +
	"contentRef\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x126\n" +
	"\x05media\x18\x03 \x01(\v2 .InstamadilloCoreTypeMedia.MediaR\x05media\x12N\n" +
	"\rxmaContentRef\x18\x04 \x01(\v2(.InstamadilloXmaContentRef.XmaContentRefR\rxmaContentRef\"\xbb\x02\n" +
	"\vPlaceholder\x12R\n" +
	"\x0fplaceholderType\x18\x01 \x01(\x0e2(.InstamadilloAddMessage.Placeholder.TypeR\x0fplaceholderType\"\xd7\x01\n" +
	"\x04Type\x12\x19\n" +
	"\x15PLACEHOLDER_TYPE_NONE\x10\x00\x12'\n" +
	"#PLACEHOLDER_TYPE_DECRYPTION_FAILURE\x10\x01\x12.\n" +
	"*PLACEHOLDER_TYPE_NOT_SUPPORTED_NEED_UPDATE\x10\x02\x12'\n" +
	"#PLACEHOLDER_TYPE_DEVICE_UNAVAILABLE\x10\x03\x122\n" +
	".PLACEHOLDER_TYPE_NOT_SUPPORTED_NOT_RECOVERABLE\x10\x04B2Z0go.mau.fi/whatsmeow/proto/instamadilloAddMessage"

var (
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescOnce sync.Once
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescData []byte
)

func file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescGZIP() []byte {
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescOnce.Do(func() {
		file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDesc), len(file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDesc)))
	})
	return file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDescData
}

var file_instamadilloAddMessage_InstamadilloAddMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_instamadilloAddMessage_InstamadilloAddMessage_proto_goTypes = []any{
	(Placeholder_Type)(0),                                 // 0: InstamadilloAddMessage.Placeholder.Type
	(*AddMessagePayload)(nil),                             // 1: InstamadilloAddMessage.AddMessagePayload
	(*AddMessageContent)(nil),                             // 2: InstamadilloAddMessage.AddMessageContent
	(*AddMessageMetadata)(nil),                            // 3: InstamadilloAddMessage.AddMessageMetadata
	(*RepliedToMessage)(nil),                              // 4: InstamadilloAddMessage.RepliedToMessage
	(*OpenMessageMicroSecondTimestamp)(nil),               // 5: InstamadilloAddMessage.OpenMessageMicroSecondTimestamp
	(*PrivateReplyInfo)(nil),                              // 6: InstamadilloAddMessage.PrivateReplyInfo
	(*ForwardingParams)(nil),                              // 7: InstamadilloAddMessage.ForwardingParams
	(*EphemeralityParams)(nil),                            // 8: InstamadilloAddMessage.EphemeralityParams
	(*Like)(nil),                                          // 9: InstamadilloAddMessage.Like
	(*ReceiverFetchXma)(nil),                              // 10: InstamadilloAddMessage.ReceiverFetchXma
	(*Placeholder)(nil),                                   // 11: InstamadilloAddMessage.Placeholder
	(*instamadilloCoreTypeText.Text)(nil),                 // 12: InstamadilloCoreTypeText.Text
	(*instamadilloCoreTypeLink.Link)(nil),                 // 13: InstamadilloCoreTypeLink.Link
	(*instamadilloCoreTypeMedia.Media)(nil),               // 14: InstamadilloCoreTypeMedia.Media
	(*instamadilloCoreTypeCollection.Collection)(nil),     // 15: InstamadilloCoreTypeCollection.Collection
	(*instamadilloCoreTypeAdminMessage.AdminMessage)(nil), // 16: InstamadilloCoreTypeAdminMessage.AdminMessage
	(*instamadilloCoreTypeActionLog.ActionLog)(nil),       // 17: InstamadilloCoreTypeActionLog.ActionLog
	(*instamadilloXmaContentRef.XmaContentRef)(nil),       // 18: InstamadilloXmaContentRef.XmaContentRef
}
var file_instamadilloAddMessage_InstamadilloAddMessage_proto_depIdxs = []int32{
	2,  // 0: InstamadilloAddMessage.AddMessagePayload.content:type_name -> InstamadilloAddMessage.AddMessageContent
	3,  // 1: InstamadilloAddMessage.AddMessagePayload.metadata:type_name -> InstamadilloAddMessage.AddMessageMetadata
	12, // 2: InstamadilloAddMessage.AddMessageContent.text:type_name -> InstamadilloCoreTypeText.Text
	9,  // 3: InstamadilloAddMessage.AddMessageContent.like:type_name -> InstamadilloAddMessage.Like
	13, // 4: InstamadilloAddMessage.AddMessageContent.link:type_name -> InstamadilloCoreTypeLink.Link
	10, // 5: InstamadilloAddMessage.AddMessageContent.receiverFetchXma:type_name -> InstamadilloAddMessage.ReceiverFetchXma
	14, // 6: InstamadilloAddMessage.AddMessageContent.media:type_name -> InstamadilloCoreTypeMedia.Media
	11, // 7: InstamadilloAddMessage.AddMessageContent.placeholder:type_name -> InstamadilloAddMessage.Placeholder
	15, // 8: InstamadilloAddMessage.AddMessageContent.collection:type_name -> InstamadilloCoreTypeCollection.Collection
	16, // 9: InstamadilloAddMessage.AddMessageContent.adminMessage:type_name -> InstamadilloCoreTypeAdminMessage.AdminMessage
	17, // 10: InstamadilloAddMessage.AddMessageContent.actionLog:type_name -> InstamadilloCoreTypeActionLog.ActionLog
	6,  // 11: InstamadilloAddMessage.AddMessageMetadata.privateReplyInfo:type_name -> InstamadilloAddMessage.PrivateReplyInfo
	4,  // 12: InstamadilloAddMessage.AddMessageMetadata.repliedToMessage:type_name -> InstamadilloAddMessage.RepliedToMessage
	7,  // 13: InstamadilloAddMessage.AddMessageMetadata.forwardingParams:type_name -> InstamadilloAddMessage.ForwardingParams
	8,  // 14: InstamadilloAddMessage.AddMessageMetadata.ephemeralityParams:type_name -> InstamadilloAddMessage.EphemeralityParams
	5,  // 15: InstamadilloAddMessage.RepliedToMessage.omMicroSecTS:type_name -> InstamadilloAddMessage.OpenMessageMicroSecondTimestamp
	14, // 16: InstamadilloAddMessage.ReceiverFetchXma.media:type_name -> InstamadilloCoreTypeMedia.Media
	18, // 17: InstamadilloAddMessage.ReceiverFetchXma.xmaContentRef:type_name -> InstamadilloXmaContentRef.XmaContentRef
	0,  // 18: InstamadilloAddMessage.Placeholder.placeholderType:type_name -> InstamadilloAddMessage.Placeholder.Type
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_instamadilloAddMessage_InstamadilloAddMessage_proto_init() }
func file_instamadilloAddMessage_InstamadilloAddMessage_proto_init() {
	if File_instamadilloAddMessage_InstamadilloAddMessage_proto != nil {
		return
	}
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes[1].OneofWrappers = []any{
		(*AddMessageContent_Text)(nil),
		(*AddMessageContent_Like)(nil),
		(*AddMessageContent_Link)(nil),
		(*AddMessageContent_ReceiverFetchXma)(nil),
		(*AddMessageContent_Media)(nil),
		(*AddMessageContent_Placeholder)(nil),
		(*AddMessageContent_Collection)(nil),
		(*AddMessageContent_AdminMessage)(nil),
		(*AddMessageContent_ActionLog)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDesc), len(file_instamadilloAddMessage_InstamadilloAddMessage_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloAddMessage_InstamadilloAddMessage_proto_goTypes,
		DependencyIndexes: file_instamadilloAddMessage_InstamadilloAddMessage_proto_depIdxs,
		EnumInfos:         file_instamadilloAddMessage_InstamadilloAddMessage_proto_enumTypes,
		MessageInfos:      file_instamadilloAddMessage_InstamadilloAddMessage_proto_msgTypes,
	}.Build()
	File_instamadilloAddMessage_InstamadilloAddMessage_proto = out.File
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_goTypes = nil
	file_instamadilloAddMessage_InstamadilloAddMessage_proto_depIdxs = nil
}
