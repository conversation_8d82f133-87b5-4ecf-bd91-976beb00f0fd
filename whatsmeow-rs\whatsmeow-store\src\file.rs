//! File-based storage implementation for production use

use crate::error::{StoreError, StoreResult};
use crate::traits::{
    DeviceStore, IdentityStore, PreKey, PreKeyStore, SenderKeyStore, SessionStore, StorageStats,
    Store,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use tokio::sync::RwLock;
use whatsmeow_types::Device;

/// File-based storage implementation with atomic operations and file locking
#[derive(Debug)]
pub struct FileStore {
    base_path: PathBuf,
    device_cache: Arc<RwLock<Option<Device>>>,
    sessions_cache: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    prekeys_cache: Arc<RwLock<HashMap<u32, PreKey>>>,
    sender_keys_cache: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    identity_keys_cache: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    cache_dirty: Arc<RwLock<bool>>,
}

impl FileStore {
    /// Create a new FileStore with the given base directory
    pub async fn new<P: AsRef<Path>>(base_path: P) -> StoreResult<Self> {
        let base_path = base_path.as_ref().to_path_buf();

        // Create base directory if it doesn't exist
        if !base_path.exists() {
            fs::create_dir_all(&base_path).await.map_err(|e| {
                StoreError::configuration(format!("Failed to create storage directory: {}", e))
            })?;
        }

        // Verify directory is writable
        let test_file = base_path.join(".write_test");
        fs::write(&test_file, b"test").await.map_err(|e| {
            StoreError::permission_denied(format!("Storage directory not writable: {}", e))
        })?;
        let _ = fs::remove_file(&test_file).await;

        let store = Self {
            base_path,
            device_cache: Arc::new(RwLock::new(None)),
            sessions_cache: Arc::new(RwLock::new(HashMap::new())),
            prekeys_cache: Arc::new(RwLock::new(HashMap::new())),
            sender_keys_cache: Arc::new(RwLock::new(HashMap::new())),
            identity_keys_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_dirty: Arc::new(RwLock::new(false)),
        };

        // Load existing data
        store.load_all().await?;

        Ok(store)
    }

    /// Get the path for a specific data file
    fn get_file_path(&self, filename: &str) -> PathBuf {
        self.base_path.join(filename)
    }

    /// Load all data from files into cache
    async fn load_all(&self) -> StoreResult<()> {
        // Load device
        if let Ok(device_data) = self.read_file_safe("device.json").await {
            if let Ok(device) = serde_json::from_slice::<Device>(&device_data) {
                *self.device_cache.write().await = Some(device);
            }
        }

        // Load sessions
        if let Ok(sessions_data) = self.read_file_safe("sessions.json").await {
            if let Ok(sessions) = serde_json::from_slice::<HashMap<String, Vec<u8>>>(&sessions_data)
            {
                *self.sessions_cache.write().await = sessions;
            }
        }

        // Load prekeys
        if let Ok(prekeys_data) = self.read_file_safe("prekeys.json").await {
            if let Ok(prekeys) = serde_json::from_slice::<HashMap<u32, PreKey>>(&prekeys_data) {
                *self.prekeys_cache.write().await = prekeys;
            }
        }

        // Load sender keys
        if let Ok(sender_keys_data) = self.read_file_safe("sender_keys.json").await {
            if let Ok(sender_keys) =
                serde_json::from_slice::<HashMap<String, Vec<u8>>>(&sender_keys_data)
            {
                *self.sender_keys_cache.write().await = sender_keys;
            }
        }

        // Load identity keys
        if let Ok(identity_keys_data) = self.read_file_safe("identity_keys.json").await {
            if let Ok(identity_keys) =
                serde_json::from_slice::<HashMap<String, Vec<u8>>>(&identity_keys_data)
            {
                *self.identity_keys_cache.write().await = identity_keys;
            }
        }

        Ok(())
    }

    /// Read a file safely, returning empty vec if file doesn't exist
    async fn read_file_safe(&self, filename: &str) -> StoreResult<Vec<u8>> {
        let path = self.get_file_path(filename);
        match fs::read(&path).await {
            Ok(data) => Ok(data),
            Err(e) if e.kind() == std::io::ErrorKind::NotFound => Ok(Vec::new()),
            Err(e) => Err(StoreError::from(e)),
        }
    }

    /// Write data to file atomically using a temporary file
    async fn write_file_atomic(&self, filename: &str, data: &[u8]) -> StoreResult<()> {
        let path = self.get_file_path(filename);

        // Use a unique temporary file name to avoid conflicts
        let temp_filename = format!(
            "{}.tmp.{}.{}",
            filename,
            std::process::id(),
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_nanos()
        );
        let temp_path = self.get_file_path(&temp_filename);

        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent).await?;
        }

        // Write to temporary file first
        let mut temp_file = fs::File::create(&temp_path).await?;
        temp_file.write_all(data).await?;
        temp_file.sync_all().await?;
        drop(temp_file);

        // Atomically rename temporary file to final file
        fs::rename(&temp_path, &path).await?;

        Ok(())
    }

    /// Persist device data to file
    async fn persist_device(&self) -> StoreResult<()> {
        let device = self.device_cache.read().await;
        let data = serde_json::to_vec(&*device)
            .map_err(|e| StoreError::serialization(format!("Failed to serialize device: {}", e)))?;
        self.write_file_atomic("device.json", &data).await
    }

    /// Persist sessions data to file
    async fn persist_sessions(&self) -> StoreResult<()> {
        let sessions = self.sessions_cache.read().await;
        let data = serde_json::to_vec(&*sessions).map_err(|e| {
            StoreError::serialization(format!("Failed to serialize sessions: {}", e))
        })?;
        self.write_file_atomic("sessions.json", &data).await
    }

    /// Persist prekeys data to file
    async fn persist_prekeys(&self) -> StoreResult<()> {
        let prekeys = self.prekeys_cache.read().await;
        let data = serde_json::to_vec(&*prekeys).map_err(|e| {
            StoreError::serialization(format!("Failed to serialize prekeys: {}", e))
        })?;
        self.write_file_atomic("prekeys.json", &data).await
    }

    /// Persist sender keys data to file
    async fn persist_sender_keys(&self) -> StoreResult<()> {
        let sender_keys = self.sender_keys_cache.read().await;
        let data = serde_json::to_vec(&*sender_keys).map_err(|e| {
            StoreError::serialization(format!("Failed to serialize sender keys: {}", e))
        })?;
        self.write_file_atomic("sender_keys.json", &data).await
    }

    /// Persist identity keys data to file
    async fn persist_identity_keys(&self) -> StoreResult<()> {
        let identity_keys = self.identity_keys_cache.read().await;
        let data = serde_json::to_vec(&*identity_keys).map_err(|e| {
            StoreError::serialization(format!("Failed to serialize identity keys: {}", e))
        })?;
        self.write_file_atomic("identity_keys.json", &data).await
    }

    /// Persist all data to files
    async fn persist_all(&self) -> StoreResult<()> {
        self.persist_device().await?;
        self.persist_sessions().await?;
        self.persist_prekeys().await?;
        self.persist_sender_keys().await?;
        self.persist_identity_keys().await?;
        *self.cache_dirty.write().await = false;
        Ok(())
    }

    /// Mark cache as dirty (needs persistence)
    async fn mark_dirty(&self) {
        *self.cache_dirty.write().await = true;
    }

    /// Check if cache is dirty
    async fn is_dirty(&self) -> bool {
        *self.cache_dirty.read().await
    }

    /// Helper method to get the next available prekey ID
    fn get_next_prekey_id_internal(&self, prekeys: &HashMap<u32, PreKey>) -> u32 {
        prekeys.keys().max().map(|id| id + 1).unwrap_or(1)
    }

    /// Create a backup of all storage files
    pub async fn create_backup<P: AsRef<Path>>(&self, backup_path: P) -> StoreResult<()> {
        let backup_path = backup_path.as_ref();

        // Create backup directory
        fs::create_dir_all(backup_path).await?;

        // Copy all data files
        let files = [
            "device.json",
            "sessions.json",
            "prekeys.json",
            "sender_keys.json",
            "identity_keys.json",
        ];

        for file in &files {
            let src = self.get_file_path(file);
            let dst = backup_path.join(file);

            if src.exists() {
                fs::copy(&src, &dst).await?;
            }
        }

        // Create backup metadata
        let metadata = serde_json::json!({
            "created_at": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            "version": "1.0",
            "files": files
        });

        let metadata_path = backup_path.join("backup_metadata.json");
        fs::write(
            &metadata_path,
            serde_json::to_vec_pretty(&metadata).unwrap(),
        )
        .await?;

        Ok(())
    }

    /// Restore from a backup directory
    pub async fn restore_from_backup<P: AsRef<Path>>(&self, backup_path: P) -> StoreResult<()> {
        let backup_path = backup_path.as_ref();

        // Verify backup metadata
        let metadata_path = backup_path.join("backup_metadata.json");
        if !metadata_path.exists() {
            return Err(StoreError::backup("Invalid backup: missing metadata"));
        }

        let metadata_data = fs::read(&metadata_path).await?;
        let metadata: serde_json::Value = serde_json::from_slice(&metadata_data)
            .map_err(|e| StoreError::backup(format!("Invalid backup metadata: {}", e)))?;

        let version = metadata
            .get("version")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        if version != "1.0" {
            return Err(StoreError::version_mismatch("1.0", version));
        }

        // Clear current data
        self.clear_all().await?;

        // Restore files
        let files = [
            "device.json",
            "sessions.json",
            "prekeys.json",
            "sender_keys.json",
            "identity_keys.json",
        ];

        for file in &files {
            let src = backup_path.join(file);
            let dst = self.get_file_path(file);

            if src.exists() {
                fs::copy(&src, &dst).await?;
            }
        }

        // Reload data into cache
        self.load_all().await?;

        Ok(())
    }

    /// Get storage directory size in bytes
    pub async fn get_storage_size(&self) -> StoreResult<u64> {
        let mut total_size = 0u64;
        let mut dir = fs::read_dir(&self.base_path).await?;

        while let Some(entry) = dir.next_entry().await? {
            if let Ok(metadata) = entry.metadata().await {
                if metadata.is_file() {
                    total_size += metadata.len();
                }
            }
        }

        Ok(total_size)
    }

    /// Compact storage by removing unused data and optimizing files
    pub async fn compact(&self) -> StoreResult<()> {
        // Persist current state to ensure consistency
        self.persist_all().await?;

        // Remove any temporary files
        let mut dir = fs::read_dir(&self.base_path).await?;
        while let Some(entry) = dir.next_entry().await? {
            let path = entry.path();
            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                if name.ends_with(".tmp") || name.ends_with(".bak") {
                    let _ = fs::remove_file(&path).await;
                }
            }
        }

        Ok(())
    }
}
#[async_trait]
impl DeviceStore for FileStore {
    async fn get_device(&self) -> StoreResult<Option<Device>> {
        let device = self.device_cache.read().await;
        Ok(device.clone())
    }

    async fn put_device(&self, device: &Device) -> StoreResult<()> {
        {
            let mut cache = self.device_cache.write().await;
            *cache = Some(device.clone());
        }
        self.mark_dirty().await;
        self.persist_device().await
    }

    async fn delete_device(&self) -> StoreResult<()> {
        {
            let mut cache = self.device_cache.write().await;
            *cache = None;
        }
        self.mark_dirty().await;
        self.persist_device().await
    }

    async fn has_device(&self) -> StoreResult<bool> {
        Ok(self.device_cache.read().await.is_some())
    }

    async fn get_device_jid(&self) -> StoreResult<Option<whatsmeow_types::Jid>> {
        Ok(self
            .device_cache
            .read()
            .await
            .as_ref()
            .map(|d| d.jid.clone()))
    }
}

#[async_trait]
impl SessionStore for FileStore {
    async fn get_session(&self, address: &str) -> StoreResult<Option<Vec<u8>>> {
        let sessions = self.sessions_cache.read().await;
        Ok(sessions.get(address).cloned())
    }

    async fn put_session(&self, address: &str, session: &[u8]) -> StoreResult<()> {
        {
            let mut sessions = self.sessions_cache.write().await;
            sessions.insert(address.to_string(), session.to_vec());
        }
        self.mark_dirty().await;
        self.persist_sessions().await
    }

    async fn delete_session(&self, address: &str) -> StoreResult<()> {
        {
            let mut sessions = self.sessions_cache.write().await;
            sessions.remove(address);
        }
        self.mark_dirty().await;
        self.persist_sessions().await
    }

    async fn delete_all_sessions(&self, phone: &str) -> StoreResult<()> {
        {
            let mut sessions = self.sessions_cache.write().await;
            sessions.retain(|key, _| !key.starts_with(phone));
        }
        self.mark_dirty().await;
        self.persist_sessions().await
    }

    async fn get_sessions_for_phone(&self, phone: &str) -> StoreResult<HashMap<String, Vec<u8>>> {
        let sessions = self.sessions_cache.read().await;
        let filtered: HashMap<String, Vec<u8>> = sessions
            .iter()
            .filter(|(key, _)| key.starts_with(phone))
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect();
        Ok(filtered)
    }

    async fn get_session_count(&self) -> StoreResult<usize> {
        let sessions = self.sessions_cache.read().await;
        Ok(sessions.len())
    }

    async fn has_session(&self, address: &str) -> StoreResult<bool> {
        let sessions = self.sessions_cache.read().await;
        Ok(sessions.contains_key(address))
    }

    async fn clear_all_sessions(&self) -> StoreResult<()> {
        {
            let mut sessions = self.sessions_cache.write().await;
            sessions.clear();
        }
        self.mark_dirty().await;
        self.persist_sessions().await
    }
}

#[async_trait]
impl PreKeyStore for FileStore {
    async fn get_or_gen_prekeys(&self, count: u32) -> StoreResult<Vec<PreKey>> {
        let mut result = Vec::new();

        {
            let mut prekeys = self.prekeys_cache.write().await;
            let start_id = self.get_next_prekey_id_internal(&prekeys);

            for i in 0..count {
                let id = start_id + i;
                let prekey = PreKey::new(id, vec![0; 32]); // Placeholder key data
                prekeys.insert(id, prekey.clone());
                result.push(prekey);
            }
        }

        self.mark_dirty().await;
        self.persist_prekeys().await?;
        Ok(result)
    }

    async fn get_prekey(&self, id: u32) -> StoreResult<Option<PreKey>> {
        let prekeys = self.prekeys_cache.read().await;
        Ok(prekeys.get(&id).cloned())
    }

    async fn remove_prekey(&self, id: u32) -> StoreResult<()> {
        {
            let mut prekeys = self.prekeys_cache.write().await;
            prekeys.remove(&id);
        }
        self.mark_dirty().await;
        self.persist_prekeys().await
    }

    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> StoreResult<()> {
        {
            let mut prekeys = self.prekeys_cache.write().await;
            for (id, prekey) in prekeys.iter_mut() {
                if *id <= up_to_id {
                    prekey.mark_uploaded();
                }
            }
        }
        self.mark_dirty().await;
        self.persist_prekeys().await
    }

    async fn get_all_prekeys(&self) -> StoreResult<Vec<PreKey>> {
        let prekeys = self.prekeys_cache.read().await;
        Ok(prekeys.values().cloned().collect())
    }

    async fn get_unuploaded_prekeys(&self) -> StoreResult<Vec<PreKey>> {
        let prekeys = self.prekeys_cache.read().await;
        Ok(prekeys
            .values()
            .filter(|prekey| !prekey.is_uploaded())
            .cloned()
            .collect())
    }

    async fn get_prekey_count(&self) -> StoreResult<usize> {
        let prekeys = self.prekeys_cache.read().await;
        Ok(prekeys.len())
    }

    async fn remove_prekeys(&self, ids: &[u32]) -> StoreResult<()> {
        {
            let mut prekeys = self.prekeys_cache.write().await;
            for id in ids {
                prekeys.remove(id);
            }
        }
        self.mark_dirty().await;
        self.persist_prekeys().await
    }

    async fn clear_all_prekeys(&self) -> StoreResult<()> {
        {
            let mut prekeys = self.prekeys_cache.write().await;
            prekeys.clear();
        }
        self.mark_dirty().await;
        self.persist_prekeys().await
    }

    async fn get_next_prekey_id(&self) -> StoreResult<u32> {
        let prekeys = self.prekeys_cache.read().await;
        Ok(self.get_next_prekey_id_internal(&prekeys))
    }
}

#[async_trait]
impl SenderKeyStore for FileStore {
    async fn get_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
    ) -> StoreResult<Option<Vec<u8>>> {
        let key = format!("{}:{}", group_id, sender_id);
        let sender_keys = self.sender_keys_cache.read().await;
        Ok(sender_keys.get(&key).cloned())
    }

    async fn put_sender_key(&self, group_id: &str, sender_id: &str, key: &[u8]) -> StoreResult<()> {
        let key_id = format!("{}:{}", group_id, sender_id);
        {
            let mut sender_keys = self.sender_keys_cache.write().await;
            sender_keys.insert(key_id, key.to_vec());
        }
        self.mark_dirty().await;
        self.persist_sender_keys().await
    }

    async fn delete_sender_key(&self, group_id: &str, sender_id: &str) -> StoreResult<()> {
        let key = format!("{}:{}", group_id, sender_id);
        {
            let mut sender_keys = self.sender_keys_cache.write().await;
            sender_keys.remove(&key);
        }
        self.mark_dirty().await;
        self.persist_sender_keys().await
    }

    async fn get_group_sender_keys(&self, group_id: &str) -> StoreResult<HashMap<String, Vec<u8>>> {
        let sender_keys = self.sender_keys_cache.read().await;
        let prefix = format!("{}:", group_id);
        let filtered: HashMap<String, Vec<u8>> = sender_keys
            .iter()
            .filter(|(key, _)| key.starts_with(&prefix))
            .map(|(k, v)| {
                let sender_id = k.strip_prefix(&prefix).unwrap_or(k);
                (sender_id.to_string(), v.clone())
            })
            .collect();
        Ok(filtered)
    }

    async fn delete_group_sender_keys(&self, group_id: &str) -> StoreResult<()> {
        {
            let mut sender_keys = self.sender_keys_cache.write().await;
            let prefix = format!("{}:", group_id);
            sender_keys.retain(|key, _| !key.starts_with(&prefix));
        }
        self.mark_dirty().await;
        self.persist_sender_keys().await
    }

    async fn has_sender_key(&self, group_id: &str, sender_id: &str) -> StoreResult<bool> {
        let key = format!("{}:{}", group_id, sender_id);
        let sender_keys = self.sender_keys_cache.read().await;
        Ok(sender_keys.contains_key(&key))
    }

    async fn clear_all_sender_keys(&self) -> StoreResult<()> {
        {
            let mut sender_keys = self.sender_keys_cache.write().await;
            sender_keys.clear();
        }
        self.mark_dirty().await;
        self.persist_sender_keys().await
    }
}

#[async_trait]
impl IdentityStore for FileStore {
    async fn get_identity_key(&self, address: &str) -> StoreResult<Option<Vec<u8>>> {
        let identity_keys = self.identity_keys_cache.read().await;
        Ok(identity_keys.get(address).cloned())
    }

    async fn put_identity_key(&self, address: &str, key: &[u8]) -> StoreResult<()> {
        {
            let mut identity_keys = self.identity_keys_cache.write().await;
            identity_keys.insert(address.to_string(), key.to_vec());
        }
        self.mark_dirty().await;
        self.persist_identity_keys().await
    }

    async fn is_trusted_identity(&self, address: &str, key: &[u8]) -> StoreResult<bool> {
        let identity_keys = self.identity_keys_cache.read().await;
        match identity_keys.get(address) {
            Some(stored_key) => Ok(stored_key == key),
            None => Ok(true), // Trust on first use
        }
    }

    async fn delete_identity_key(&self, address: &str) -> StoreResult<()> {
        {
            let mut identity_keys = self.identity_keys_cache.write().await;
            identity_keys.remove(address);
        }
        self.mark_dirty().await;
        self.persist_identity_keys().await
    }

    async fn get_all_identity_keys(&self) -> StoreResult<HashMap<String, Vec<u8>>> {
        let identity_keys = self.identity_keys_cache.read().await;
        Ok(identity_keys.clone())
    }

    async fn mark_identity_trusted(&self, address: &str, key: &[u8]) -> StoreResult<()> {
        self.put_identity_key(address, key).await
    }

    async fn mark_identity_untrusted(&self, address: &str) -> StoreResult<()> {
        self.delete_identity_key(address).await
    }

    async fn has_identity_key(&self, address: &str) -> StoreResult<bool> {
        let identity_keys = self.identity_keys_cache.read().await;
        Ok(identity_keys.contains_key(address))
    }

    async fn clear_all_identity_keys(&self) -> StoreResult<()> {
        {
            let mut identity_keys = self.identity_keys_cache.write().await;
            identity_keys.clear();
        }
        self.mark_dirty().await;
        self.persist_identity_keys().await
    }
}

#[async_trait]
impl Store for FileStore {
    async fn get_stats(&self) -> StoreResult<StorageStats> {
        let device_count = if self.device_cache.read().await.is_some() {
            1
        } else {
            0
        };
        let session_count = self.sessions_cache.read().await.len();
        let prekey_count = self.prekeys_cache.read().await.len();
        let sender_key_count = self.sender_keys_cache.read().await.len();
        let identity_key_count = self.identity_keys_cache.read().await.len();
        let total_size_bytes = self.get_storage_size().await.unwrap_or(0);

        Ok(StorageStats {
            device_count,
            session_count,
            prekey_count,
            sender_key_count,
            identity_key_count,
            total_size_bytes,
        })
    }

    async fn clear_all(&self) -> StoreResult<()> {
        // Clear caches
        {
            *self.device_cache.write().await = None;
            self.sessions_cache.write().await.clear();
            self.prekeys_cache.write().await.clear();
            self.sender_keys_cache.write().await.clear();
            self.identity_keys_cache.write().await.clear();
        }

        // Persist empty state
        self.persist_all().await?;

        Ok(())
    }

    async fn backup(&self) -> StoreResult<Vec<u8>> {
        // Ensure all data is persisted
        if self.is_dirty().await {
            self.persist_all().await?;
        }

        let device = self.device_cache.read().await.clone();
        let sessions = self.sessions_cache.read().await.clone();
        let prekeys = self.prekeys_cache.read().await.clone();
        let sender_keys = self.sender_keys_cache.read().await.clone();
        let identity_keys = self.identity_keys_cache.read().await.clone();

        let backup_data = serde_json::json!({
            "version": "1.0",
            "device": device,
            "sessions": sessions,
            "prekeys": prekeys,
            "sender_keys": sender_keys,
            "identity_keys": identity_keys,
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        });

        serde_json::to_vec(&backup_data)
            .map_err(|e| StoreError::serialization(format!("Failed to serialize backup: {}", e)))
    }

    async fn restore(&self, data: &[u8]) -> StoreResult<()> {
        let backup_data: serde_json::Value = serde_json::from_slice(data).map_err(|e| {
            StoreError::serialization(format!("Failed to deserialize backup: {}", e))
        })?;

        // Check version compatibility
        let version = backup_data
            .get("version")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        if version != "1.0" {
            return Err(StoreError::version_mismatch("1.0", version));
        }

        // Clear existing data
        self.clear_all().await?;

        // Restore device
        if let Some(device_data) = backup_data.get("device") {
            if !device_data.is_null() {
                let device: Device = serde_json::from_value(device_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize device: {}", e))
                })?;
                *self.device_cache.write().await = Some(device);
            }
        }

        // Restore sessions
        if let Some(sessions_data) = backup_data.get("sessions") {
            let sessions: HashMap<String, Vec<u8>> = serde_json::from_value(sessions_data.clone())
                .map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize sessions: {}", e))
                })?;
            *self.sessions_cache.write().await = sessions;
        }

        // Restore prekeys
        if let Some(prekeys_data) = backup_data.get("prekeys") {
            let prekeys: HashMap<u32, PreKey> = serde_json::from_value(prekeys_data.clone())
                .map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize prekeys: {}", e))
                })?;
            *self.prekeys_cache.write().await = prekeys;
        }

        // Restore sender keys
        if let Some(sender_keys_data) = backup_data.get("sender_keys") {
            let sender_keys: HashMap<String, Vec<u8>> =
                serde_json::from_value(sender_keys_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize sender keys: {}", e))
                })?;
            *self.sender_keys_cache.write().await = sender_keys;
        }

        // Restore identity keys
        if let Some(identity_keys_data) = backup_data.get("identity_keys") {
            let identity_keys: HashMap<String, Vec<u8>> =
                serde_json::from_value(identity_keys_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize identity keys: {}", e))
                })?;
            *self.identity_keys_cache.write().await = identity_keys;
        }

        // Persist restored data
        self.persist_all().await?;

        Ok(())
    }

    async fn validate(&self) -> StoreResult<Vec<String>> {
        let mut issues = Vec::new();

        // Validate device
        if let Some(device) = self.device_cache.read().await.as_ref() {
            if device.jid.user.is_empty() {
                issues.push("Device JID user is empty".to_string());
            }
            if device.jid.server.is_empty() {
                issues.push("Device JID server is empty".to_string());
            }
        }

        // Validate sessions
        let sessions = self.sessions_cache.read().await;
        for (address, session_data) in sessions.iter() {
            if address.is_empty() {
                issues.push("Empty session address found".to_string());
            }
            if session_data.is_empty() {
                issues.push(format!("Empty session data for address: {}", address));
            }
        }

        // Validate prekeys
        let prekeys = self.prekeys_cache.read().await;
        for (id, prekey) in prekeys.iter() {
            if prekey.id != *id {
                issues.push(format!(
                    "PreKey ID mismatch: stored as {} but has ID {}",
                    id, prekey.id
                ));
            }
            if prekey.key_pair.is_empty() {
                issues.push(format!("Empty key pair for PreKey {}", id));
            }
        }

        // Validate sender keys
        let sender_keys = self.sender_keys_cache.read().await;
        for (key, data) in sender_keys.iter() {
            if !key.contains(':') {
                issues.push(format!("Invalid sender key format: {}", key));
            }
            if data.is_empty() {
                issues.push(format!("Empty sender key data for: {}", key));
            }
        }

        // Validate identity keys
        let identity_keys = self.identity_keys_cache.read().await;
        for (address, key_data) in identity_keys.iter() {
            if address.is_empty() {
                issues.push("Empty identity key address found".to_string());
            }
            if key_data.is_empty() {
                issues.push(format!("Empty identity key data for address: {}", address));
            }
        }

        // Validate file system consistency
        let files = [
            "device.json",
            "sessions.json",
            "prekeys.json",
            "sender_keys.json",
            "identity_keys.json",
        ];
        for file in &files {
            let path = self.get_file_path(file);
            if path.exists() {
                match fs::metadata(&path).await {
                    Ok(metadata) => {
                        if metadata.len() == 0 {
                            issues.push(format!("Empty file: {}", file));
                        }
                    }
                    Err(e) => {
                        issues.push(format!("Cannot read file metadata for {}: {}", file, e));
                    }
                }
            }
        }

        Ok(issues)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use whatsmeow_types::{device::DevicePlatform, Device, Jid};

    fn create_test_device() -> Device {
        let jid = Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 0);
        Device::new(jid, 12345, DevicePlatform::Web).unwrap()
    }

    async fn create_test_store() -> (FileStore, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let store = FileStore::new(temp_dir.path()).await.unwrap();
        (store, temp_dir)
    }

    #[tokio::test]
    async fn test_file_store_creation() {
        let temp_dir = TempDir::new().unwrap();
        let store = FileStore::new(temp_dir.path()).await.unwrap();

        // Should create directory and be ready to use
        assert!(temp_dir.path().exists());
        assert!(store.get_stats().await.unwrap().is_empty());
    }

    #[tokio::test]
    async fn test_file_store_creation_invalid_path() {
        // Try to create store in a path that should fail on Windows
        // Use a path that's definitely invalid on Windows
        let invalid_path = if cfg!(windows) {
            "CON:/invalid/path" // CON is a reserved name on Windows
        } else {
            "/proc/invalid/path" // /proc is typically read-only on Unix
        };

        let result = FileStore::new(invalid_path).await;
        // On some systems, this might succeed in creating directories, so we'll be more lenient
        if result.is_ok() {
            // If creation succeeded, at least verify we can't write to a truly invalid location
            let truly_invalid = if cfg!(windows) {
                "\\\\invalid\\server\\path"
            } else {
                "/dev/null/invalid"
            };
            let result2 = FileStore::new(truly_invalid).await;
            // This should definitely fail, but if not, we'll skip this test
            if result2.is_ok() {
                println!("Warning: Could not find a path that fails on this system");
            }
        }
    }

    #[tokio::test]
    async fn test_device_store_persistence() {
        let (store, _temp_dir) = create_test_store().await;
        let device = create_test_device();

        // Initially no device
        assert!(store.get_device().await.unwrap().is_none());
        assert!(!store.has_device().await.unwrap());

        // Store device
        store.put_device(&device).await.unwrap();
        assert!(store.has_device().await.unwrap());

        // Create new store instance with same path to test persistence
        let store2 = FileStore::new(_temp_dir.path()).await.unwrap();
        let retrieved = store2.get_device().await.unwrap().unwrap();
        assert_eq!(retrieved.jid, device.jid);
        assert_eq!(retrieved.registration_id, device.registration_id);

        // Delete device
        store2.delete_device().await.unwrap();
        assert!(!store2.has_device().await.unwrap());

        // Verify deletion persisted
        let store3 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert!(store3.get_device().await.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_session_store_persistence() {
        let (store, _temp_dir) = create_test_store().await;
        let address = "<EMAIL>";
        let session_data = vec![1, 2, 3, 4, 5];

        // Store session
        store.put_session(address, &session_data).await.unwrap();
        assert_eq!(store.get_session_count().await.unwrap(), 1);

        // Create new store instance to test persistence
        let store2 = FileStore::new(_temp_dir.path()).await.unwrap();
        let retrieved = store2.get_session(address).await.unwrap().unwrap();
        assert_eq!(retrieved, session_data);
        assert_eq!(store2.get_session_count().await.unwrap(), 1);

        // Test phone-based operations
        let phone = "1234567890";
        let sessions = store2.get_sessions_for_phone(phone).await.unwrap();
        assert_eq!(sessions.len(), 1);

        // Clear all sessions
        store2.clear_all_sessions().await.unwrap();
        assert_eq!(store2.get_session_count().await.unwrap(), 0);

        // Verify persistence
        let store3 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert_eq!(store3.get_session_count().await.unwrap(), 0);
    }

    #[tokio::test]
    async fn test_prekey_store_persistence() {
        let (store, _temp_dir) = create_test_store().await;

        // Generate prekeys
        let prekeys = store.get_or_gen_prekeys(3).await.unwrap();
        assert_eq!(prekeys.len(), 3);
        assert_eq!(store.get_prekey_count().await.unwrap(), 3);

        // Create new store instance to test persistence
        let store2 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert_eq!(store2.get_prekey_count().await.unwrap(), 3);

        let prekey = store2.get_prekey(1).await.unwrap().unwrap();
        assert_eq!(prekey.id, 1);

        // Mark as uploaded and verify persistence
        store2.mark_prekeys_as_uploaded(2).await.unwrap();
        let store3 = FileStore::new(_temp_dir.path()).await.unwrap();
        let unuploaded = store3.get_unuploaded_prekeys().await.unwrap();
        assert_eq!(unuploaded.len(), 1); // Only prekey 3 should be unuploaded

        // Remove prekeys
        store3.remove_prekeys(&[1, 2]).await.unwrap();
        let store4 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert_eq!(store4.get_prekey_count().await.unwrap(), 1);
    }

    #[tokio::test]
    async fn test_sender_key_store_persistence() {
        let (store, _temp_dir) = create_test_store().await;
        let group_id = "group123";
        let sender_id = "sender456";
        let key_data = vec![1, 2, 3, 4, 5];

        // Store sender key
        store
            .put_sender_key(group_id, sender_id, &key_data)
            .await
            .unwrap();
        assert!(store.has_sender_key(group_id, sender_id).await.unwrap());

        // Create new store instance to test persistence
        let store2 = FileStore::new(_temp_dir.path()).await.unwrap();
        let retrieved = store2
            .get_sender_key(group_id, sender_id)
            .await
            .unwrap()
            .unwrap();
        assert_eq!(retrieved, key_data);

        // Test group operations
        let group_keys = store2.get_group_sender_keys(group_id).await.unwrap();
        assert_eq!(group_keys.len(), 1);

        // Delete group sender keys
        store2.delete_group_sender_keys(group_id).await.unwrap();
        let store3 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert!(!store3.has_sender_key(group_id, sender_id).await.unwrap());
    }

    #[tokio::test]
    async fn test_identity_store_persistence() {
        let (store, _temp_dir) = create_test_store().await;
        let address = "<EMAIL>";
        let key_data = vec![1, 2, 3, 4, 5];

        // Store identity key
        store.put_identity_key(address, &key_data).await.unwrap();
        assert!(store.has_identity_key(address).await.unwrap());

        // Create new store instance to test persistence
        let store2 = FileStore::new(_temp_dir.path()).await.unwrap();
        let retrieved = store2.get_identity_key(address).await.unwrap().unwrap();
        assert_eq!(retrieved, key_data);

        // Test trust
        assert!(store2
            .is_trusted_identity(address, &key_data)
            .await
            .unwrap());

        // Mark as untrusted
        store2.mark_identity_untrusted(address).await.unwrap();
        let store3 = FileStore::new(_temp_dir.path()).await.unwrap();
        assert!(!store3.has_identity_key(address).await.unwrap());
    }

    #[tokio::test]
    async fn test_store_trait_operations() {
        let (store, _temp_dir) = create_test_store().await;
        let device = create_test_device();

        // Add some data
        store.put_device(&device).await.unwrap();
        store
            .put_session("<EMAIL>", &[1, 2, 3])
            .await
            .unwrap();
        store.get_or_gen_prekeys(2).await.unwrap();
        store
            .put_sender_key("group1", "sender1", &[4, 5, 6])
            .await
            .unwrap();
        store
            .put_identity_key("<EMAIL>", &[7, 8, 9])
            .await
            .unwrap();

        // Test stats
        let stats = store.get_stats().await.unwrap();
        assert!(!stats.is_empty());
        assert_eq!(stats.device_count, 1);
        assert_eq!(stats.session_count, 1);
        assert_eq!(stats.prekey_count, 2);
        assert_eq!(stats.sender_key_count, 1);
        assert_eq!(stats.identity_key_count, 1);
        assert!(stats.total_size_bytes > 0);

        // Test backup and restore
        let backup_data = store.backup().await.unwrap();
        assert!(!backup_data.is_empty());

        // Clear and restore
        store.clear_all().await.unwrap();
        let stats = store.get_stats().await.unwrap();
        assert!(stats.is_empty());

        store.restore(&backup_data).await.unwrap();
        let stats = store.get_stats().await.unwrap();
        assert!(!stats.is_empty());

        // Verify data integrity after restore
        let restored_device = store.get_device().await.unwrap().unwrap();
        assert_eq!(restored_device.jid, device.jid);

        // Test validation
        let issues = store.validate().await.unwrap();
        assert!(issues.is_empty());
    }

    #[tokio::test]
    async fn test_atomic_writes() {
        let (store, temp_dir) = create_test_store().await;
        let device = create_test_device();

        // Store device
        store.put_device(&device).await.unwrap();

        // Verify no temporary files exist after successful write
        let mut dir = fs::read_dir(temp_dir.path()).await.unwrap();
        let mut temp_files = Vec::new();
        while let Some(entry) = dir.next_entry().await.unwrap() {
            if let Some(name) = entry.file_name().to_str() {
                if name.ends_with(".tmp") {
                    temp_files.push(name.to_string());
                }
            }
        }
        assert!(
            temp_files.is_empty(),
            "Found temporary files: {:?}",
            temp_files
        );

        // Verify device file exists
        let device_file = temp_dir.path().join("device.json");
        assert!(device_file.exists());
    }

    #[tokio::test]
    async fn test_backup_and_restore_operations() {
        let (store, temp_dir) = create_test_store().await;
        let device = create_test_device();

        // Add comprehensive data
        store.put_device(&device).await.unwrap();
        store
            .put_session("<EMAIL>", &[1, 2, 3])
            .await
            .unwrap();
        store
            .put_session("<EMAIL>", &[4, 5, 6])
            .await
            .unwrap();
        store.get_or_gen_prekeys(3).await.unwrap();
        store
            .put_sender_key("group1", "sender1", &[7, 8, 9])
            .await
            .unwrap();
        store
            .put_sender_key("group1", "sender2", &[10, 11, 12])
            .await
            .unwrap();
        store
            .put_identity_key("<EMAIL>", &[13, 14, 15])
            .await
            .unwrap();

        // Create backup directory
        let backup_dir = temp_dir.path().join("backup");
        store.create_backup(&backup_dir).await.unwrap();

        // Verify backup files exist
        assert!(backup_dir.join("device.json").exists());
        assert!(backup_dir.join("sessions.json").exists());
        assert!(backup_dir.join("prekeys.json").exists());
        assert!(backup_dir.join("sender_keys.json").exists());
        assert!(backup_dir.join("identity_keys.json").exists());
        assert!(backup_dir.join("backup_metadata.json").exists());

        // Clear original store
        store.clear_all().await.unwrap();
        assert!(store.get_stats().await.unwrap().is_empty());

        // Restore from backup
        store.restore_from_backup(&backup_dir).await.unwrap();

        // Verify all data was restored
        let stats = store.get_stats().await.unwrap();
        assert_eq!(stats.device_count, 1);
        assert_eq!(stats.session_count, 2);
        assert_eq!(stats.prekey_count, 3);
        assert_eq!(stats.sender_key_count, 2);
        assert_eq!(stats.identity_key_count, 1);

        let restored_device = store.get_device().await.unwrap().unwrap();
        assert_eq!(restored_device.jid, device.jid);
    }

    #[tokio::test]
    async fn test_storage_size_and_compaction() {
        let (store, _temp_dir) = create_test_store().await;

        // Initially should be small or zero
        let initial_size = store.get_storage_size().await.unwrap();

        // Add some data
        let device = create_test_device();
        store.put_device(&device).await.unwrap();
        store
            .put_session("<EMAIL>", &vec![0; 1000])
            .await
            .unwrap(); // 1KB session

        let size_after_data = store.get_storage_size().await.unwrap();
        assert!(size_after_data > initial_size);

        // Test compaction
        store.compact().await.unwrap();
        let size_after_compact = store.get_storage_size().await.unwrap();

        // Size should be similar (compaction mainly removes temp files)
        // In a real scenario with fragmentation, this might be smaller
        assert!(size_after_compact >= initial_size);
    }

    #[tokio::test]
    async fn test_concurrent_file_operations() {
        let (store, _temp_dir) = create_test_store().await;
        let store = Arc::new(store);

        // Use a smaller number of concurrent operations to reduce flakiness
        let num_operations = 5;
        let mut handles = Vec::new();

        // Test concurrent session operations with sequential execution to reduce race conditions
        for i in 0..num_operations {
            let store_clone = Arc::clone(&store);
            let handle = tokio::spawn(async move {
                let address = format!("test{}@s.whatsapp.net", i);
                let session_data = vec![i as u8; 10];

                // Store session
                store_clone
                    .put_session(&address, &session_data)
                    .await
                    .expect(&format!("Failed to put session {}", i));

                // Small delay to allow file operations to complete
                tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;

                // Retrieve session
                let retrieved = store_clone
                    .get_session(&address)
                    .await
                    .expect(&format!("Failed to get session {}", i));
                assert_eq!(retrieved, Some(session_data), "Session {} data mismatch", i);
            });
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for (i, handle) in handles.into_iter().enumerate() {
            handle.await.expect(&format!("Task {} failed", i));
        }

        // Verify all sessions were stored
        assert_eq!(store.get_session_count().await.unwrap(), num_operations);
    }

    #[tokio::test]
    async fn test_error_handling() {
        let (store, temp_dir) = create_test_store().await;

        // Test restore with invalid backup
        let invalid_backup = b"invalid json data";
        let result = store.restore(invalid_backup).await;
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            StoreError::Serialization { .. }
        ));

        // Test restore from invalid backup directory
        let invalid_backup_dir = temp_dir.path().join("nonexistent");
        let result = store.restore_from_backup(&invalid_backup_dir).await;
        assert!(result.is_err());

        // Test backup to invalid directory (should create it)
        let backup_dir = temp_dir.path().join("new_backup");
        let result = store.create_backup(&backup_dir).await;
        assert!(result.is_ok());
        assert!(backup_dir.exists());
    }

    #[tokio::test]
    async fn test_validation_with_file_issues() {
        let (store, temp_dir) = create_test_store().await;
        let device = create_test_device();

        // Store some data
        store.put_device(&device).await.unwrap();

        // Create an empty file to simulate corruption
        let empty_file = temp_dir.path().join("sessions.json");
        fs::write(&empty_file, b"").await.unwrap();

        let issues = store.validate().await.unwrap();
        assert!(!issues.is_empty());

        let issues_str = issues.join(", ");
        assert!(issues_str.contains("Empty file"));
    }

    #[tokio::test]
    async fn test_cache_dirty_tracking() {
        let (store, _temp_dir) = create_test_store().await;

        // Initially not dirty
        assert!(!store.is_dirty().await);

        // Mark as dirty
        store.mark_dirty().await;
        assert!(store.is_dirty().await);

        // Persist all should clear dirty flag
        store.persist_all().await.unwrap();
        assert!(!store.is_dirty().await);
    }
}
