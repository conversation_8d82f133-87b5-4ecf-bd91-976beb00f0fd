// Go test vector generator for whatsmeow binary compatibility testing
// This program generates test vectors using the original Go whatsmeow library
// to ensure exact behavioral compatibility with the Rust implementation.

package main

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"go.mau.fi/whatsmeow/binary"
	"go.mau.fi/whatsmeow/types"
)

// TestVector represents a single test case with input and expected output
type TestVector struct {
	Name          string       `json:"name"`
	Description   string       `json:"description"`
	InputBytes    []byte       `json:"input_bytes"`
	InputHex      string       `json:"input_hex"`
	ExpectedNode  *NodeResult  `json:"expected_node,omitempty"`
	ExpectedError *ErrorResult `json:"expected_error,omitempty"`
}

// NodeResult represents the expected parsed node structure
type NodeResult struct {
	Tag        string                 `json:"tag"`
	Attributes map[string]interface{} `json:"attributes"`
	Content    ContentResult          `json:"content"`
}

// ContentResult represents different types of node content
type ContentResult struct {
	Type     string       `json:"type"` // "none", "text", "binary", "children"
	Value    interface{}  `json:"value,omitempty"`
	Children []NodeResult `json:"children,omitempty"`
}

// ErrorResult represents expected error conditions
type ErrorResult struct {
	Type    string `json:"type"`
	Message string `json:"message,omitempty"`
	Code    int    `json:"code,omitempty"`
}

func main() {
	fmt.Println("Generating Go whatsmeow test vectors...")

	vectors := generateTestVectors()

	// Write test vectors to JSON file
	output, err := json.MarshalIndent(vectors, "", "  ")
	if err != nil {
		log.Fatal("Failed to marshal test vectors:", err)
	}

	err = os.WriteFile("test_vectors.json", output, 0644)
	if err != nil {
		log.Fatal("Failed to write test vectors:", err)
	}

	fmt.Printf("Generated %d test vectors\n", len(vectors))

	// Also run the tests to verify Go behavior
	runCompatibilityTests(vectors)
}

func generateTestVectors() []TestVector {
	var vectors []TestVector

	// 1. Basic serialization/deserialization tests
	vectors = append(vectors, generateBasicNodeTests()...)

	// 2. Empty vs null content disambiguation
	vectors = append(vectors, generateEmptyContentTests()...)

	// 3. Special byte handling
	vectors = append(vectors, generateSpecialByteTests()...)

	// 4. Error condition tests
	vectors = append(vectors, generateErrorTests()...)

	// 5. JID handling tests
	vectors = append(vectors, generateJIDTests()...)

	// 6. Packed string tests
	vectors = append(vectors, generatePackedStringTests()...)

	// 7. List boundary tests
	vectors = append(vectors, generateListBoundaryTests()...)

	return vectors
}

func generateBasicNodeTests() []TestVector {
	var vectors []TestVector

	// Test 1: Minimal node with empty content
	node1 := binary.Node{
		Tag:     "message",
		Attrs:   make(binary.Attrs),
		Content: "",
	}
	bytes1, _ := binary.Marshal(node1)
	vectors = append(vectors, TestVector{
		Name:        "minimal_node_empty_content",
		Description: "Minimal node with empty string content",
		InputBytes:  bytes1,
		InputHex:    hex.EncodeToString(bytes1),
		ExpectedNode: &NodeResult{
			Tag:        "message",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "binary", // Go treats empty string as binary after serialization
				Value: []byte{},
			},
		},
	})

	// Test 2: Node with text content
	node2 := binary.Node{
		Tag:     "message",
		Attrs:   make(binary.Attrs),
		Content: "hello world",
	}
	bytes2, _ := binary.Marshal(node2)
	vectors = append(vectors, TestVector{
		Name:        "node_with_text_content",
		Description: "Node with UTF-8 text content",
		InputBytes:  bytes2,
		InputHex:    hex.EncodeToString(bytes2),
		ExpectedNode: &NodeResult{
			Tag:        "message",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "text",
				Value: "hello world",
			},
		},
	})

	// Test 3: Node with binary content
	binaryData := []byte{0xFF, 0x00, 0x80, 0xFE}
	node3 := binary.Node{
		Tag:     "message",
		Attrs:   make(binary.Attrs),
		Content: binaryData,
	}
	bytes3, _ := binary.Marshal(node3)
	vectors = append(vectors, TestVector{
		Name:        "node_with_binary_content",
		Description: "Node with non-UTF8 binary content",
		InputBytes:  bytes3,
		InputHex:    hex.EncodeToString(bytes3),
		ExpectedNode: &NodeResult{
			Tag:        "message",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "binary",
				Value: binaryData,
			},
		},
	})

	// Test 4: Node with attributes
	node4 := binary.Node{
		Tag: "message",
		Attrs: binary.Attrs{
			"id":   "test123",
			"type": "chat",
		},
		Content: "",
	}
	bytes4, _ := binary.Marshal(node4)
	vectors = append(vectors, TestVector{
		Name:        "node_with_attributes",
		Description: "Node with multiple attributes",
		InputBytes:  bytes4,
		InputHex:    hex.EncodeToString(bytes4),
		ExpectedNode: &NodeResult{
			Tag: "message",
			Attributes: map[string]interface{}{
				"id":   "test123",
				"type": "chat",
			},
			Content: ContentResult{
				Type:  "binary",
				Value: []byte{},
			},
		},
	})

	return vectors
}

func generateEmptyContentTests() []TestVector {
	var vectors []TestVector

	// Critical test: Empty string vs empty binary disambiguation
	// This is the main compatibility issue between Go and Rust

	// Test 1: Empty string content
	emptyStringNode := binary.Node{
		Tag:     "test",
		Attrs:   make(binary.Attrs),
		Content: "",
	}
	emptyStringBytes, _ := binary.Marshal(emptyStringNode)

	// Test 2: Empty binary content
	emptyBinaryNode := binary.Node{
		Tag:     "test",
		Attrs:   make(binary.Attrs),
		Content: []byte{},
	}
	emptyBinaryBytes, _ := binary.Marshal(emptyBinaryNode)

	vectors = append(vectors, TestVector{
		Name:        "empty_string_serialization",
		Description: "Empty string content serialization - critical for compatibility",
		InputBytes:  emptyStringBytes,
		InputHex:    hex.EncodeToString(emptyStringBytes),
		ExpectedNode: &NodeResult{
			Tag:        "test",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "binary", // Go behavior: empty string becomes binary after roundtrip
				Value: []byte{},
			},
		},
	})

	vectors = append(vectors, TestVector{
		Name:        "empty_binary_serialization",
		Description: "Empty binary content serialization",
		InputBytes:  emptyBinaryBytes,
		InputHex:    hex.EncodeToString(emptyBinaryBytes),
		ExpectedNode: &NodeResult{
			Tag:        "test",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "binary",
				Value: []byte{},
			},
		},
	})

	return vectors
}

func generateSpecialByteTests() []TestVector {
	var vectors []TestVector

	// Test binary content containing protocol token bytes
	// These must remain as binary and not be interpreted as tokens
	protocolBytes := []byte{
		248, // LIST_8
		249, // LIST_16
		252, // BINARY_8
		253, // BINARY_20
		254, // BINARY_32
		255, // NIBBLE_8
	}

	specialNode := binary.Node{
		Tag:     "data",
		Attrs:   make(binary.Attrs),
		Content: protocolBytes,
	}
	specialBytes, _ := binary.Marshal(specialNode)

	vectors = append(vectors, TestVector{
		Name:        "binary_with_protocol_tokens",
		Description: "Binary content containing protocol token bytes - must preserve as binary",
		InputBytes:  specialBytes,
		InputHex:    hex.EncodeToString(specialBytes),
		ExpectedNode: &NodeResult{
			Tag:        "data",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "binary",
				Value: protocolBytes,
			},
		},
	})

	return vectors
}

func generateErrorTests() []TestVector {
	var vectors []TestVector

	// Test 1: Truncated BINARY_8
	vectors = append(vectors, TestVector{
		Name:        "truncated_binary_8",
		Description: "BINARY_8 token without size byte",
		InputBytes:  []byte{252}, // BINARY_8 without size
		InputHex:    "fc",
		ExpectedError: &ErrorResult{
			Type:    "UnexpectedEndOfData",
			Message: "unexpected end of data",
		},
	})

	// Test 2: Invalid token
	vectors = append(vectors, TestVector{
		Name:        "invalid_single_token",
		Description: "Invalid single-byte token",
		InputBytes:  []byte{248, 2, 200}, // LIST_8, size=2, invalid token 200
		InputHex:    "f802c8",
		ExpectedError: &ErrorResult{
			Type:    "InvalidToken",
			Message: "invalid token",
			Code:    200,
		},
	})

	// Test 3: List size too small
	vectors = append(vectors, TestVector{
		Name:        "list_size_too_small",
		Description: "List size too small for valid node",
		InputBytes:  []byte{248, 1, 19}, // LIST_8, size=1, "message"
		InputHex:    "f80113",
		ExpectedError: &ErrorResult{
			Type:    "InvalidNodeFormat",
			Message: "node must have attributes or content",
		},
	})

	return vectors
}

func generateJIDTests() []TestVector {
	var vectors []TestVector

	// Test JID pair handling
	jid := types.NewJID("user", "s.whatsapp.net")
	jidNode := binary.Node{
		Tag:     "message",
		Attrs:   make(binary.Attrs),
		Content: jid,
	}
	jidBytes, _ := binary.Marshal(jidNode)

	vectors = append(vectors, TestVector{
		Name:        "jid_pair_content",
		Description: "JID pair as node content",
		InputBytes:  jidBytes,
		InputHex:    hex.EncodeToString(jidBytes),
		ExpectedNode: &NodeResult{
			Tag:        "message",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "text",
				Value: "<EMAIL>",
			},
		},
	})

	return vectors
}

func generatePackedStringTests() []TestVector {
	var vectors []TestVector

	// Test nibble-packed strings
	// Note: This requires manual construction as Go whatsmeow handles this internally

	// Nibble-packed "123" (odd length)
	nibbleBytes := []byte{
		248, 2, 19, // LIST_8, size=2, "message"
		255, // NIBBLE_8
		129, // Length byte: 1 with odd flag (0x80 | 1)
		18,  // Packed nibbles: 1=0x01, 2=0x02 -> 0x12
	}

	vectors = append(vectors, TestVector{
		Name:        "nibble_packed_odd_length",
		Description: "Nibble-packed string with odd length",
		InputBytes:  nibbleBytes,
		InputHex:    hex.EncodeToString(nibbleBytes),
		ExpectedNode: &NodeResult{
			Tag:        "message",
			Attributes: make(map[string]interface{}),
			Content: ContentResult{
				Type:  "text",
				Value: "12", // Odd length nibble string
			},
		},
	})

	return vectors
}

func generateListBoundaryTests() []TestVector {
	var vectors []TestVector

	// Test LIST_8 to LIST_16 boundary (255 -> 256 items)
	// This is critical for compatibility

	
	// Create a node with exactly 255 items (LIST_8 boundary)
	attrs := make(binary.Attrs)
	for i := 0; i < 127; i++ { // 127 key-value pairs = 254 items + 1 tag = 255 total
		attrs[fmt.Sprintf("key%d", i)] = ""
	}

	boundaryNode := binary.Node{
		Tag:     "test",
		Attrs:   attrs,
		Content: nil,
	}
	boundaryBytes, _ := binary.Marshal(boundaryNode)

	vectors = append(vectors, TestVector{
		Name:        "list_8_boundary",
		Description: "Node at LIST_8 boundary (255 items)",
		InputBytes:  boundaryBytes,
		InputHex:    hex.EncodeToString(boundaryBytes),
		ExpectedNode: &NodeResult{
			Tag:        "test",
			Attributes: attrs,
			Content: ContentResult{
				Type: "none",
			},
		},
	})

	return vectors
}

func runCompatibilityTests(vectors []TestVector) {
	fmt.Println("\nRunning Go compatibility tests...")

	passed := 0
	failed := 0

	for _, vector := range vectors {
		fmt.Printf("Testing: %s\n", vector.Name)

		// Parse the input bytes using Go whatsmeow
		node, err := binary.Unmarshal(vector.InputBytes)

		if vector.ExpectedError != nil {
			// Expecting an error
			if err == nil {
				fmt.Printf("  FAIL: Expected error but got success: %+v\n", node)
				failed++
				continue
			}
			fmt.Printf("  PASS: Got expected error: %v\n", err)
			passed++
		} else if vector.ExpectedNode != nil {
			// Expecting success
			if err != nil {
				fmt.Printf("  FAIL: Expected success but got error: %v\n", err)
				failed++
				continue
			}

			// Verify the parsed node matches expectations
			if verifyNode(node, vector.ExpectedNode) {
				fmt.Printf("  PASS: Node matches expected structure\n")
				passed++
			} else {
				fmt.Printf("  FAIL: Node structure mismatch\n")
				fmt.Printf("    Expected: %+v\n", vector.ExpectedNode)
				fmt.Printf("    Actual: %+v\n", nodeToResult(node))
				failed++
			}
		}
	}

	fmt.Printf("\nTest Results: %d passed, %d failed\n", passed, failed)
}

func verifyNode(actual *binary.Node, expected *NodeResult) bool {
	if actual.Tag != expected.Tag {
		return false
	}

	// Check attributes
	if len(actual.Attrs) != len(expected.Attributes) {
		return false
	}

	for key, expectedValue := range expected.Attributes {
		actualValue, exists := actual.Attrs[key]
		if !exists || actualValue != expectedValue {
			return false
		}
	}

	// Check content type and value
	return verifyContent(actual.Content, expected.Content)
}

func verifyContent(actual interface{}, expected ContentResult) bool {
	switch expected.Type {
	case "none":
		return actual == nil
	case "text":
		if str, ok := actual.(string); ok {
			return str == expected.Value.(string)
		}
		return false
	case "binary":
		if bytes, ok := actual.([]byte); ok {
			expectedBytes := expected.Value.([]byte)
			if len(bytes) != len(expectedBytes) {
				return false
			}
			for i, b := range bytes {
				if b != expectedBytes[i] {
					return false
				}
			}
			return true
		}
		return false
	case "children":
		if children, ok := actual.([]binary.Node); ok {
			if len(children) != len(expected.Children) {
				return false
			}
			for i, child := range children {
				if !verifyNode(&child, &expected.Children[i]) {
					return false
				}
			}
			return true
		}
		return false
	}
	return false
}

func nodeToResult(node *binary.Node) *NodeResult {
	result := &NodeResult{
		Tag:        node.Tag,
		Attributes: make(map[string]interface{}),
	}

	for key, value := range node.Attrs {
		result.Attributes[key] = value
	}

	switch content := node.Content.(type) {
	case nil:
		result.Content = ContentResult{Type: "none"}
	case string:
		result.Content = ContentResult{Type: "text", Value: content}
	case []byte:
		result.Content = ContentResult{Type: "binary", Value: content}
	case []binary.Node:
		children := make([]NodeResult, len(content))
		for i, child := range content {
			children[i] = *nodeToResult(&child)
		}
		result.Content = ContentResult{Type: "children", Children: children}
	}

	return result
}
