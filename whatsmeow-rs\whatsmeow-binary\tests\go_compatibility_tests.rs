//! Comprehensive Go-Rust compatibility tests for whatsmeow binary parsing
//!
//! These tests ensure exact behavioral compatibility between the Go whatsmeow
//! library and the Rust whatsmeow-binary implementation.

use std::collections::HashMap;
use whatsmeow_binary::{AttributeValue, BinaryError, Node, NodeContent};

/// Test vectors generated from Go whatsmeow implementation
/// Each test case includes input data, expected output, and error conditions
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct TestVector {
    name: &'static str,
    input_bytes: Vec<u8>,
    expected_result: Result<ExpectedNode, ExpectedError>,
    description: &'static str,
}

#[derive(Debug, <PERSON>lone, PartialEq)]
struct ExpectedNode {
    tag: String,
    attributes: HashMap<String, String>,
    content: ExpectedContent,
}

#[derive(Debug, <PERSON>lone, PartialEq)]
enum ExpectedContent {
    None,
    Text(String),
    Binary(Vec<u8>),
    Children(Vec<ExpectedNode>),
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq)]
enum ExpectedError {
    InvalidTag(u8),
    UnexpectedEndOfData,
    InvalidNodeFormat(String),
}

/// Test vectors that match Go whatsmeow behavior exactly
fn get_go_compatibility_test_vectors() -> Vec<TestVector> {
    vec![
        // Basic node serialization/deserialization
        TestVector {
            name: "minimal_node_with_empty_content",
            input_bytes: vec![248, 2, 19, 252, 0], // LIST_8, size=2, "message", BINARY_8, len=0
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Binary(vec![]), // Go treats empty content as binary
            }),
            description: "Minimal node with empty binary content - matches Go behavior",
        },
        TestVector {
            name: "node_with_text_content",
            input_bytes: vec![248, 2, 19, 252, 5, 104, 101, 108, 108, 111], // "hello"
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Text("hello".to_string()),
            }),
            description: "Node with UTF-8 text content",
        },
        TestVector {
            name: "node_with_binary_content",
            input_bytes: vec![248, 2, 19, 252, 3, 255, 0, 128], // Non-UTF8 binary data
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Binary(vec![255, 0, 128]),
            }),
            description: "Node with binary content containing non-UTF8 bytes",
        },
        // Special byte value handling - critical for Go compatibility
        TestVector {
            name: "binary_with_protocol_tokens",
            input_bytes: vec![248, 2, 19, 252, 4, 248, 249, 252, 253], // Contains LIST_8, LIST_16, BINARY_8, BINARY_20
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Binary(vec![248, 249, 252, 253]), // Must remain binary
            }),
            description: "Binary content with protocol token bytes - must preserve as binary",
        },
        // Empty vs null handling - major compatibility issue
        TestVector {
            name: "empty_string_content",
            input_bytes: vec![248, 2, 19, 252, 0], // Empty string serialized by Go
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Binary(vec![]), // Go behavior: empty content becomes binary
            }),
            description: "Empty string content - Go serializes as BINARY_8 with length 0",
        },
        // Node with attributes
        TestVector {
            name: "node_with_attributes",
            input_bytes: vec![248, 4, 19, 8, 252, 4, 116, 101, 115, 116, 252, 0], // message, id="test", empty content
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: {
                    let mut attrs = HashMap::new();
                    attrs.insert("id".to_string(), "test".to_string());
                    attrs
                },
                content: ExpectedContent::Binary(vec![]),
            }),
            description: "Node with single attribute and empty content",
        },
        // Error cases that must match Go behavior exactly
        TestVector {
            name: "truncated_binary_8",
            input_bytes: vec![252], // BINARY_8 without size byte
            expected_result: Err(ExpectedError::UnexpectedEndOfData),
            description: "Truncated BINARY_8 token - Go returns EOF error",
        },
        TestVector {
            name: "invalid_token",
            input_bytes: vec![245], // Just invalid token 245
            expected_result: Err(ExpectedError::InvalidTag(245)),
            description: "Invalid single-byte token - Go returns token error",
        },
        TestVector {
            name: "invalid_list_size_too_small",
            input_bytes: vec![248, 1, 19], // LIST_8, size=1, "message" - no content/attributes
            expected_result: Err(ExpectedError::InvalidNodeFormat(
                "Node must have attributes or content".to_string(),
            )),
            description: "List size too small for valid node - Go validation error",
        },
        // JID handling - WhatsApp specific
        TestVector {
            name: "jid_pair_node",
            input_bytes: vec![
                248, 2, 19, 250, 252, 4, 117, 115, 101, 114, 252, 14, 115, 46, 119, 104, 97, 116,
                115, 97, 112, 112, 46, 110, 101, 116,
            ], // JID_PAIR
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Text("<EMAIL>".to_string()),
            }),
            description: "JID pair content - WhatsApp specific format",
        },
        // Packed string formats
        TestVector {
            name: "nibble_packed_string",
            input_bytes: vec![248, 2, 19, 255, 130, 18, 52], // NIBBLE_8 packed "1234"
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Text("123".to_string()), // Odd length nibble
            }),
            description: "Nibble-packed string with odd length",
        },
        // List size boundaries - critical for compatibility
        TestVector {
            name: "list_8_boundary",
            input_bytes: create_large_node_bytes(255), // Exactly at LIST_8 boundary
            expected_result: Ok(create_expected_large_node(255)),
            description: "Node at LIST_8/LIST_16 boundary (255 items)",
        },
        // Deeply nested structures
        TestVector {
            name: "nested_children",
            input_bytes: vec![248, 2, 19, 248, 1, 248, 1, 7], // message with nested empty lists
            expected_result: Ok(ExpectedNode {
                tag: "message".to_string(),
                attributes: HashMap::new(),
                content: ExpectedContent::Children(vec![ExpectedNode {
                    tag: "receipt".to_string(),
                    attributes: HashMap::new(),
                    content: ExpectedContent::None,
                }]),
            }),
            description: "Nested child nodes",
        },
    ]
}

// Helper functions for generating test data
fn create_large_node_bytes(size: usize) -> Vec<u8> {
    let mut bytes = vec![248, size as u8, 19]; // LIST_8, size, "message"
                                               // Add pairs of attributes (key="k", value="v")
    for _ in 0..(size - 1) / 2 {
        bytes.extend_from_slice(&[252, 1, 107, 252, 1, 118]); // key="k", value="v"
    }
    bytes
}

fn create_expected_large_node(size: usize) -> ExpectedNode {
    let mut attributes = HashMap::new();
    for _ in 0..(size - 1) / 2 {
        attributes.insert("k".to_string(), "v".to_string());
    }
    ExpectedNode {
        tag: "message".to_string(),
        attributes,
        content: ExpectedContent::None,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_go_compatibility_vectors() {
        let test_vectors = get_go_compatibility_test_vectors();

        for vector in test_vectors {
            println!("Testing: {} - {}", vector.name, vector.description);

            let result = Node::parse(&vector.input_bytes);

            match (&result, &vector.expected_result) {
                (Ok(node), Ok(expected)) => {
                    assert_node_matches(node, expected, vector.name);
                }
                (Err(error), Err(expected_error)) => {
                    assert_error_matches(error, expected_error, vector.name);
                }
                (Ok(node), Err(expected_error)) => {
                    panic!(
                        "Test '{}': Expected error {:?}, but got success: {:?}",
                        vector.name, expected_error, node
                    );
                }
                (Err(error), Ok(expected)) => {
                    panic!(
                        "Test '{}': Expected success {:?}, but got error: {:?}",
                        vector.name, expected, error
                    );
                }
            }
        }
    }

    #[test]
    fn test_serialization_roundtrip_compatibility() {
        // Test that our serialization produces the same bytes as Go
        let test_cases = vec![
            (
                Node::with_text("message", "hello"),
                vec![248, 2, 19, 252, 5, 104, 101, 108, 108, 111],
                "Text content serialization",
            ),
            (
                Node::with_binary("message", vec![255, 0, 128]),
                vec![248, 2, 19, 252, 3, 255, 0, 128],
                "Binary content serialization",
            ),
            (
                Node::with_children("message", vec![]),
                vec![248, 2, 19, 0], // LIST_EMPTY for empty children
                "Empty children serialization",
            ),
        ];

        for (node, expected_bytes, description) in test_cases {
            println!("Testing serialization: {}", description);

            let serialized = node.serialize().expect("Serialization should succeed");
            assert_eq!(
                serialized, expected_bytes,
                "Serialization mismatch for {}: expected {:?}, got {:?}",
                description, expected_bytes, serialized
            );

            // Test roundtrip
            let parsed = Node::parse(&serialized).expect("Roundtrip parsing should succeed");
            let reserialized = parsed.serialize().expect("Re-serialization should succeed");
            assert_eq!(
                serialized, reserialized,
                "Roundtrip mismatch for {}",
                description
            );
        }
    }

    #[test]
    fn test_error_condition_compatibility() {
        // Test that error conditions match Go exactly
        let error_cases = vec![
            (
                vec![252],
                "BINARY_8 without size",
                ExpectedError::UnexpectedEndOfData,
            ),
            (
                vec![253, 0],
                "BINARY_20 incomplete",
                ExpectedError::UnexpectedEndOfData,
            ),
            (
                vec![254, 0, 0, 0],
                "BINARY_32 incomplete",
                ExpectedError::UnexpectedEndOfData,
            ),
            (
                vec![248, 2, 245],
                "Invalid token 245",
                ExpectedError::InvalidTag(245),
            ),
            (
                vec![248, 1, 19],
                "List too small",
                ExpectedError::InvalidNodeFormat(
                    "Node must have attributes or content".to_string(),
                ),
            ),
        ];

        for (input, description, expected_error) in error_cases {
            println!("Testing error case: {}", description);

            let result = Node::parse(&input);
            match result {
                Err(error) => {
                    assert_error_matches(&error, &expected_error, description);
                }
                Ok(node) => {
                    panic!(
                        "Expected error for '{}', but got success: {:?}",
                        description, node
                    );
                }
            }
        }
    }

    #[test]
    fn test_special_byte_handling_compatibility() {
        // Test that special protocol bytes are handled exactly like Go
        let special_byte_cases = vec![
            (
                vec![248, 2, 19, 252, 1, 248],
                "LIST_8 in content",
                ExpectedContent::Binary(vec![248]),
            ),
            (
                vec![248, 2, 19, 252, 1, 252],
                "BINARY_8 in content",
                ExpectedContent::Binary(vec![252]),
            ),
            (
                vec![248, 2, 19, 252, 1, 255],
                "NIBBLE_8 in content",
                ExpectedContent::Binary(vec![255]),
            ),
            (
                vec![248, 2, 19, 252, 1, 0],
                "NULL byte in content",
                ExpectedContent::Binary(vec![0]),
            ),
        ];

        for (input, description, expected_content) in special_byte_cases {
            println!("Testing special bytes: {}", description);

            let node = Node::parse(&input).expect("Should parse successfully");
            assert_eq!(node.tag, "message");

            match (&node.content, &expected_content) {
                (NodeContent::Binary(actual), ExpectedContent::Binary(expected)) => {
                    assert_eq!(
                        actual, expected,
                        "Binary content mismatch for {}",
                        description
                    );
                }
                (NodeContent::Text(actual), ExpectedContent::Text(expected)) => {
                    assert_eq!(
                        actual, expected,
                        "Text content mismatch for {}",
                        description
                    );
                }
                _ => {
                    panic!(
                        "Content type mismatch for {}: expected {:?}, got {:?}",
                        description, expected_content, node.content
                    );
                }
            }
        }
    }

    // Helper functions for test assertions
    fn assert_node_matches(actual: &Node, expected: &ExpectedNode, test_name: &str) {
        assert_eq!(
            actual.tag, expected.tag,
            "Tag mismatch in test '{}'",
            test_name
        );

        // Check attributes
        assert_eq!(
            actual.attributes.len(),
            expected.attributes.len(),
            "Attribute count mismatch in test '{}'",
            test_name
        );

        for (key, expected_value) in &expected.attributes {
            match actual.get_attribute(key) {
                Some(AttributeValue::String(actual_value)) => {
                    assert_eq!(
                        actual_value, expected_value,
                        "Attribute '{}' mismatch in test '{}'",
                        key, test_name
                    );
                }
                Some(other) => {
                    panic!(
                        "Attribute '{}' type mismatch in test '{}': expected String, got {:?}",
                        key, test_name, other
                    );
                }
                None => {
                    panic!("Missing attribute '{}' in test '{}'", key, test_name);
                }
            }
        }

        // Check content
        match (&actual.content, &expected.content) {
            (NodeContent::None, ExpectedContent::None) => {}
            (NodeContent::Text(actual), ExpectedContent::Text(expected)) => {
                assert_eq!(
                    actual, expected,
                    "Text content mismatch in test '{}'",
                    test_name
                );
            }
            (NodeContent::Binary(actual), ExpectedContent::Binary(expected)) => {
                assert_eq!(
                    actual, expected,
                    "Binary content mismatch in test '{}'",
                    test_name
                );
            }
            (NodeContent::Children(actual), ExpectedContent::Children(expected)) => {
                assert_eq!(
                    actual.len(),
                    expected.len(),
                    "Children count mismatch in test '{}'",
                    test_name
                );
                for (i, (actual_child, expected_child)) in
                    actual.iter().zip(expected.iter()).enumerate()
                {
                    assert_node_matches(
                        actual_child,
                        expected_child,
                        &format!("{}[child {}]", test_name, i),
                    );
                }
            }
            _ => {
                panic!(
                    "Content type mismatch in test '{}': expected {:?}, got {:?}",
                    test_name, expected.content, actual.content
                );
            }
        }
    }

    fn assert_error_matches(actual: &BinaryError, expected: &ExpectedError, test_name: &str) {
        match (actual, expected) {
            (BinaryError::InvalidTag(actual_tag), ExpectedError::InvalidTag(expected_tag)) => {
                assert_eq!(
                    actual_tag, expected_tag,
                    "InvalidTag mismatch in test '{}'",
                    test_name
                );
            }
            (BinaryError::UnexpectedEndOfData, ExpectedError::UnexpectedEndOfData) => {}
            (
                BinaryError::InvalidNodeFormat(actual_msg),
                ExpectedError::InvalidNodeFormat(expected_msg),
            ) => {
                assert_eq!(
                    actual_msg, expected_msg,
                    "InvalidNodeFormat message mismatch in test '{}'",
                    test_name
                );
            }
            _ => {
                panic!(
                    "Error type mismatch in test '{}': expected {:?}, got {:?}",
                    test_name, expected, actual
                );
            }
        }
    }
}
