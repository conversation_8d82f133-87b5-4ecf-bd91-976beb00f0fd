Code Smells & Structure Issues
1. Large Implementation Block The NoiseHandshake impl is quite large (~400+ lines). Consider splitting it into logical modules:

// Split into separate modules
mod handshake_core;
mod encryption;
mod key_derivation;
mod state_machine;
2. Magic Numbers & Constants You have some hardcoded values that should be constants:

// Add these constants at the top
const X25519_KEY_SIZE: usize = 32;
const AES_GCM_TAG_SIZE: usize = 16;
const HKDF_OUTPUT_SIZE: usize = 64;
const IV_SIZE: usize = 12;
const IV_COUNTER_OFFSET: usize = 8;
3. Error Handling Improvements Your error handling could be more specific:

#[derive(Debug, thiserror::Error)]
pub enum NoiseError {
    #[error("Handshake failed: {0}")]
    HandshakeFailed(String),
    #[error("Invalid state: expected {expected}, got {actual}")]
    InvalidState { expected: String, actual: String },
    #[error("Invalid key length: expected {expected}, got {actual}")]
    InvalidKeyLength { expected: usize, actual: usize },
    #[error("Encryption failed: {0}")]
    EncryptionFailed(#[from] aes_gcm::Error),
    #[error("Key derivation failed: {0}")]
    KeyDerivationFailed(#[from] crate::error::CryptoError),
}
Design Pattern Suggestions
1. State Machine Pattern Your handshake states could benefit from a proper state machine:

pub trait HandshakeState {
    fn next_action(&self) -> Result<HandshakeAction, NoiseError>;
    fn transition(self, action: HandshakeAction) -> Result<Box<dyn HandshakeState>, NoiseError>;
}

pub enum HandshakeAction {
    CreateClientHello,
    ProcessServerHello(ServerHello),
    CreateClientFinish,
    Complete,
}
2. Builder Pattern for Configuration Consider a builder for handshake configuration:

pub struct NoiseHandshakeBuilder {
    static_key: Option<KeyPair>,
    pattern: Option<&'static [u8]>,
    header: Option<&'static [u8]>,
}

impl NoiseHandshakeBuilder {
    pub fn new() -> Self { /* ... */ }
    pub fn with_static_key(mut self, key: KeyPair) -> Self { /* ... */ }
    pub fn with_pattern(mut self, pattern: &'static [u8]) -> Self { /* ... */ }
    pub fn build(self) -> Result<NoiseHandshake, NoiseError> { /* ... */ }
}
Best Practices & Improvements
1. Memory Safety & Zeroization Sensitive key material should be zeroized:

use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(ZeroizeOnDrop)]
pub struct NoiseKeys {
    pub write_key: Vec<u8>,
    pub read_key: Vec<u8>,
}
2. Const Generics for Key Sizes Make key sizes compile-time constants:

pub struct NoiseHandshake<const KEY_SIZE: usize = 32> {
    // Use [u8; KEY_SIZE] instead of Vec<u8> where appropriate
}
3. Method Extraction Break down large methods like process_server_hello:

impl NoiseHandshake {
    pub fn process_server_hello(&mut self, server_hello: &ServerHello) -> Result<Vec<u8>, NoiseError> {
        self.validate_state_for_server_hello()?;
        self.validate_server_hello(server_hello)?;
        
        self.process_server_ephemeral(&server_hello.ephemeral)?;
        let server_static = self.decrypt_server_static(&server_hello.static_ciphertext)?;
        self.process_server_static(&server_static)?;
        
        let _cert_data = self.decrypt_and_verify_certificate(&server_hello.payload_ciphertext)?;
        
        self.create_client_finish_message()
    }
    
    fn validate_state_for_server_hello(&self) -> Result<(), NoiseError> {
        if self.state != HandshakeState::ClientHelloSent {
            return Err(NoiseError::InvalidState {
                expected: "ClientHelloSent".to_string(),
                actual: format!("{:?}", self.state),
            });
        }
        Ok(())
    }
}
Performance Optimizations
1. Reduce Allocations Use stack-allocated arrays where possible:

fn generate_iv(&self, counter: u32) -> [u8; 12] {
    let mut iv = [0u8; 12];
    iv[8..12].copy_from_slice(&counter.to_be_bytes());
    iv
}
2. Batch Operations Consider batching hash updates:

pub fn authenticate_batch(&mut self, data_items: &[&[u8]]) {
    let mut hasher = Sha256::new();
    hasher.update(&self.hash);
    for data in data_items {
        hasher.update(data);
    }
    self.hash = hasher.finalize().to_vec();
}
3. Lazy Initialization Only create cipher when needed:

fn get_or_create_cipher(&mut self) -> Result<&Aes256Gcm, NoiseError> {
    if self.key.is_none() {
        return Err(NoiseError::HandshakeFailed("No encryption key available".to_string()));
    }
    Ok(self.key.as_ref().unwrap())
}
Documentation & Readability
1. Add Module-Level Documentation

//! # Noise Protocol Implementation
//! 
//! This module implements the Noise_XX_25519_AESGCM_SHA256 handshake pattern
//! as specified in the Noise Protocol Framework specification.
//! 
//! ## Usage
//! 
//! ```rust
//! let static_key = KeyPair::generate_x25519()?;
//! let mut handshake = NoiseHandshake::new(static_key)?;
//! let client_hello = handshake.create_client_hello()?;
//! ```
2. Better Method Documentation

/// Encrypts plaintext using the current handshake state.
/// 
/// # Arguments
/// * `plaintext` - The data to encrypt
/// 
/// # Returns
/// * `Ok(Vec<u8>)` - The encrypted ciphertext with authentication tag
/// * `Err(NoiseError)` - If encryption fails or no key is available
/// 
/// # Security Notes
/// Each call increments an internal counter to ensure unique nonces.
pub fn encrypt(&mut self, plaintext: &[u8]) -> Result<Vec<u8>, NoiseError> {
    // ...
}
Maintainability Improvements
1. Configuration Struct Extract configuration into a separate struct:

#[derive(Debug, Clone)]
pub struct NoiseConfig {
    pub pattern: &'static [u8],
    pub header: &'static [u8],
    pub max_message_size: usize,
}

impl Default for NoiseConfig {
    fn default() -> Self {
        Self {
            pattern: NOISE_PATTERN,
            header: WA_CONN_HEADER,
            max_message_size: 1024 * 1024, // 1MB
        }
    }
}
2. Trait-Based Design Consider traits for different handshake patterns:

pub trait NoisePattern {
    const PATTERN_NAME: &'static str;
    fn create_handshake(static_key: KeyPair) -> Result<Box<dyn NoiseHandshake>, NoiseError>;
}

pub struct NoiseXX;
impl NoisePattern for NoiseXX {
    const PATTERN_NAME: &'static str = "Noise_XX_25519_AESGCM_SHA256";
    // ...
}
These improvements would make your code more maintainable, performant, and easier to test while following Rust best practices. The key is to implement these changes incrementally to avoid breaking existing functionality.