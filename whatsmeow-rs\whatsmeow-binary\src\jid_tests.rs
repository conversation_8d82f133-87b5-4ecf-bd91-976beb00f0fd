//! JID (J<PERSON>ber ID) handling tests to match Go implementation

use crate::node::{AttributeValue, Node};

#[test]
fn test_jid_pair_serialization() {
    // Test basic JID pair (user@server)
    let node = Node::new("test").with_attribute(
        "jid",
        AttributeValue::JID("<EMAIL>".to_string()),
    );

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    // JID should be preserved in some form
    assert!(deserialized.has_attribute("jid"));
}

#[test]
fn test_server_only_jid() {
    // Test server-only JID (no user part)
    let node =
        Node::new("test").with_attribute("jid", AttributeValue::JID("s.whatsapp.net".to_string()));

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert!(deserialized.has_attribute("jid"));
}

#[test]
fn test_empty_user_jid() {
    // Test JID with empty user part (@server)
    let node =
        Node::new("test").with_attribute("jid", AttributeValue::JID("@s.whatsapp.net".to_string()));

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert!(deserialized.has_attribute("jid"));
}

#[test]
fn test_group_jid() {
    // Test group JID format
    let group_jid = "<EMAIL>";
    let node = Node::new("test").with_attribute("jid", AttributeValue::JID(group_jid.to_string()));

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert!(deserialized.has_attribute("jid"));
}

#[test]
fn test_broadcast_jid() {
    // Test broadcast JID format
    let broadcast_jid = "status@broadcast";
    let node =
        Node::new("test").with_attribute("jid", AttributeValue::JID(broadcast_jid.to_string()));

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert!(deserialized.has_attribute("jid"));
}

#[test]
fn test_special_jid_formats() {
    // Test various special JID formats that WhatsApp uses
    let special_jids = vec![
        "<EMAIL>", // Regular user
        "<EMAIL>", // Group
        "status@broadcast",           // Status broadcast
        "<EMAIL>",      // Server JID
    ];

    for jid in special_jids {
        let node = Node::new("test").with_attribute("from", AttributeValue::JID(jid.to_string()));

        let serialized = node
            .serialize()
            .unwrap_or_else(|_| panic!("Should serialize JID: {}", jid));
        let deserialized =
            Node::parse(&serialized).unwrap_or_else(|_| panic!("Should deserialize JID: {}", jid));

        assert!(
            deserialized.has_attribute("from"),
            "JID should be preserved: {}",
            jid
        );
    }
}

#[test]
fn test_jid_in_content() {
    // Test JID as content (not just attributes)
    let jid = "<EMAIL>";
    let node = Node::with_text("jid", jid);

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert_eq!(deserialized.text(), Some(&jid.to_string()));
}

#[test]
fn test_multiple_jids_in_node() {
    // Test node with multiple JID attributes
    let node = Node::new("message")
        .with_attribute(
            "from",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute(
            "to",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute(
            "participant",
            AttributeValue::JID("<EMAIL>".to_string()),
        );

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("to"));
    assert!(deserialized.has_attribute("participant"));
}

#[test]
fn test_invalid_jid_formats() {
    // Test handling of malformed JIDs
    let invalid_jids = vec![
        "",                  // Empty JID
        "@",                 // Just @
        "user@",             // Missing server
        "@server",           // Missing user (but this might be valid)
        "user@@server",      // Double @
        "user@server@extra", // Multiple @
    ];

    for invalid_jid in invalid_jids {
        let node =
            Node::new("test").with_attribute("jid", AttributeValue::JID(invalid_jid.to_string()));

        // Should still serialize (JID validation might be application-level)
        let result = node.serialize();
        if let Ok(serialized) = result {
            let deserialized = Node::parse(&serialized).expect("Should deserialize");
            assert!(deserialized.has_attribute("jid"));
        }
    }
}

#[test]
fn test_jid_with_special_characters() {
    // Test JIDs with special characters that might need escaping
    let special_jids = vec![
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ];

    for jid in special_jids {
        let node = Node::new("test").with_attribute("jid", AttributeValue::JID(jid.to_string()));

        let serialized = node
            .serialize()
            .unwrap_or_else(|_| panic!("Should serialize JID: {}", jid));
        let deserialized =
            Node::parse(&serialized).unwrap_or_else(|_| panic!("Should deserialize JID: {}", jid));

        assert!(deserialized.has_attribute("jid"));
    }
}

#[test]
fn test_jid_case_sensitivity() {
    // Test case sensitivity in JIDs
    let jids = vec![
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ];

    for jid in jids {
        let node = Node::new("test").with_attribute("jid", AttributeValue::JID(jid.to_string()));

        let serialized = node.serialize().expect("Should serialize");
        let deserialized = Node::parse(&serialized).expect("Should deserialize");

        assert!(deserialized.has_attribute("jid"));
    }
}

#[test]
fn test_numeric_jids() {
    // Test purely numeric JIDs (phone numbers)
    let numeric_jids = vec![
        "<EMAIL>",
        "<EMAIL>", // Very long number
        "<EMAIL>",                    // Single digit
    ];

    for jid in numeric_jids {
        let node = Node::new("test").with_attribute("jid", AttributeValue::JID(jid.to_string()));

        let serialized = node.serialize().expect("Should serialize");
        let deserialized = Node::parse(&serialized).expect("Should deserialize");

        assert!(deserialized.has_attribute("jid"));
    }
}
