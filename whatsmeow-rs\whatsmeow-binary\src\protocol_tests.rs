//! Protocol-specific tests for WhatsApp binary format edge cases

use crate::node::{AttributeValue, Node, NodeContent};

#[test]
fn test_whatsapp_specific_tokens() {
    // Test tokens that are specific to WhatsApp protocol
    let whatsapp_tokens = vec![
        "s.whatsapp.net",
        "g.us",
        "urn:xmpp:whatsapp:push",
        "w:profile:picture",
        "mmg.whatsapp.net",
        "mmg-fallback.whatsapp.net",
    ];

    for token in whatsapp_tokens {
        let node = Node::with_text("test", token);
        let serialized = node
            .serialize()
            .unwrap_or_else(|_| panic!("Should serialize token: {}", token));
        let deserialized = Node::parse(&serialized)
            .unwrap_or_else(|_| panic!("Should deserialize token: {}", token));
        assert_eq!(deserialized.text(), Some(&token.to_string()));
    }
}

#[test]
fn test_message_structure() {
    // Test typical WhatsApp message structure
    let _message = Node::new("message")
        .with_attribute(
            "from",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute(
            "to",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute("id", "message-id-123")
        .with_attribute("type", "text")
        .with_attribute("t", 1234567890i64);

    let text_node = Node::with_text("body", "Hello, World!");
    let message_with_body = Node::new("message")
        .with_attribute(
            "from",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute(
            "to",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute("id", "message-id-123")
        .with_attribute("type", "text");

    let message_with_body = Node {
        tag: message_with_body.tag,
        attributes: message_with_body.attributes,
        content: NodeContent::Children(vec![text_node]),
    };

    let serialized = message_with_body
        .serialize()
        .expect("Should serialize message");
    let deserialized = Node::parse(&serialized).expect("Should deserialize message");

    assert_eq!(deserialized.tag, "message");
    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("to"));
    assert!(deserialized.has_attribute("id"));
    assert!(deserialized.has_attribute("type"));

    let children = deserialized.children().expect("Should have children");
    assert_eq!(children.len(), 1);
    assert_eq!(children[0].tag, "body");
    assert_eq!(children[0].text(), Some(&"Hello, World!".to_string()));
}

#[test]
fn test_presence_stanza() {
    // Test WhatsApp presence stanza structure
    let presence = Node::new("presence")
        .with_attribute(
            "from",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute("type", "available");

    let serialized = presence.serialize().expect("Should serialize presence");
    let deserialized = Node::parse(&serialized).expect("Should deserialize presence");

    assert_eq!(deserialized.tag, "presence");
    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("type"));
}

#[test]
fn test_iq_stanza() {
    // Test WhatsApp IQ (Info/Query) stanza structure
    let iq = Node::new("iq")
        .with_attribute("id", "iq-123")
        .with_attribute("type", "get")
        .with_attribute("xmlns", "urn:xmpp:ping");

    let ping_node = Node::new("ping");
    let iq_with_ping = Node {
        tag: iq.tag,
        attributes: iq.attributes,
        content: NodeContent::Children(vec![ping_node]),
    };

    let serialized = iq_with_ping.serialize().expect("Should serialize IQ");
    let deserialized = Node::parse(&serialized).expect("Should deserialize IQ");

    assert_eq!(deserialized.tag, "iq");
    assert!(deserialized.has_attribute("id"));
    assert!(deserialized.has_attribute("type"));
    assert!(deserialized.has_attribute("xmlns"));

    let children = deserialized.children().expect("Should have children");
    assert_eq!(children.len(), 1);
    assert_eq!(children[0].tag, "ping");
}

#[test]
fn test_media_message_structure() {
    // Test media message with binary data
    let media_data = vec![0xFF, 0xD8, 0xFF, 0xE0]; // JPEG header
    let media_node = Node::with_binary("media", media_data.clone());

    let message = Node::new("message")
        .with_attribute("type", "image")
        .with_attribute("id", "media-msg-123");

    let message_with_media = Node {
        tag: message.tag,
        attributes: message.attributes,
        content: NodeContent::Children(vec![media_node]),
    };

    let serialized = message_with_media
        .serialize()
        .expect("Should serialize media message");
    let deserialized = Node::parse(&serialized).expect("Should deserialize media message");

    assert_eq!(deserialized.tag, "message");
    let children = deserialized.children().expect("Should have children");
    assert_eq!(children.len(), 1);
    assert_eq!(children[0].tag, "media");
    assert_eq!(children[0].binary(), Some(&media_data));
}

#[test]
fn test_group_message_structure() {
    // Test group message with participant
    let group_message = Node::new("message")
        .with_attribute("from", AttributeValue::JID("<EMAIL>".to_string()))
        .with_attribute(
            "participant",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute("id", "group-msg-123")
        .with_attribute("type", "text");

    let serialized = group_message
        .serialize()
        .expect("Should serialize group message");
    let deserialized = Node::parse(&serialized).expect("Should deserialize group message");

    assert_eq!(deserialized.tag, "message");
    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("participant"));
    assert!(deserialized.has_attribute("id"));
    assert!(deserialized.has_attribute("type"));
}

#[test]
fn test_receipt_message() {
    // Test read receipt structure
    let receipt = Node::new("receipt")
        .with_attribute(
            "from",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute(
            "to",
            AttributeValue::JID("<EMAIL>".to_string()),
        )
        .with_attribute("id", "msg-id-to-ack")
        .with_attribute("type", "read")
        .with_attribute("t", 1234567890i64);

    let serialized = receipt.serialize().expect("Should serialize receipt");
    let deserialized = Node::parse(&serialized).expect("Should deserialize receipt");

    assert_eq!(deserialized.tag, "receipt");
    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("to"));
    assert!(deserialized.has_attribute("id"));
    assert!(deserialized.has_attribute("type"));
    assert!(deserialized.has_attribute("t"));
}

#[test]
fn test_notification_structure() {
    // Test notification message structure
    let notification = Node::new("notification")
        .with_attribute("from", AttributeValue::JID("s.whatsapp.net".to_string()))
        .with_attribute("id", "notif-123")
        .with_attribute("type", "server");

    let serialized = notification
        .serialize()
        .expect("Should serialize notification");
    let deserialized = Node::parse(&serialized).expect("Should deserialize notification");

    assert_eq!(deserialized.tag, "notification");
    assert!(deserialized.has_attribute("from"));
    assert!(deserialized.has_attribute("id"));
    assert!(deserialized.has_attribute("type"));
}

#[test]
fn test_stream_management() {
    // Test stream management elements
    let stream_elements = vec![
        ("r", vec![]),             // Request ack
        ("a", vec![("h", "123")]), // Ack with handled count
    ];

    for (tag, attrs) in stream_elements {
        let mut node = Node::new(tag);
        for (key, value) in attrs {
            node.set_attribute(key, value);
        }

        let serialized = node
            .serialize()
            .unwrap_or_else(|_| panic!("Should serialize {}", tag));
        let deserialized =
            Node::parse(&serialized).unwrap_or_else(|_| panic!("Should deserialize {}", tag));
        assert_eq!(deserialized.tag, tag);
    }
}

#[test]
fn test_error_stanzas() {
    // Test error stanza structures
    let error_node = Node::new("error")
        .with_attribute("code", "404")
        .with_attribute("type", "cancel");

    let text_node = Node::with_text("text", "Item not found");
    let error_with_text = Node {
        tag: error_node.tag,
        attributes: error_node.attributes,
        content: NodeContent::Children(vec![text_node]),
    };

    let serialized = error_with_text.serialize().expect("Should serialize error");
    let deserialized = Node::parse(&serialized).expect("Should deserialize error");

    assert_eq!(deserialized.tag, "error");
    assert!(deserialized.has_attribute("code"));
    assert!(deserialized.has_attribute("type"));

    let children = deserialized.children().expect("Should have children");
    assert_eq!(children.len(), 1);
    assert_eq!(children[0].tag, "text");
}

#[test]
fn test_timestamp_handling() {
    // Test various timestamp formats used in WhatsApp
    let timestamps = vec![
        1234567890i64,    // Unix timestamp
        1234567890123i64, // Unix timestamp in milliseconds
        0i64,             // Zero timestamp
        i64::MAX,         // Maximum timestamp
    ];

    for timestamp in timestamps {
        let node = Node::new("message").with_attribute("t", timestamp);

        let serialized = node.serialize().expect("Should serialize timestamp");
        let deserialized = Node::parse(&serialized).expect("Should deserialize timestamp");

        assert!(deserialized.has_attribute("t"));
    }
}

#[test]
fn test_boolean_attributes() {
    // Test boolean attribute handling
    let node = Node::new("test")
        .with_attribute("read", true)
        .with_attribute("delivered", false)
        .with_attribute("encrypted", true);

    let serialized = node.serialize().expect("Should serialize booleans");
    let deserialized = Node::parse(&serialized).expect("Should deserialize booleans");

    assert!(deserialized.has_attribute("read"));
    assert!(deserialized.has_attribute("delivered"));
    assert!(deserialized.has_attribute("encrypted"));
}

#[test]
fn test_complex_nested_structure() {
    // Test complex nested structure similar to WhatsApp protocol
    let inner_node = Node::with_text("body", "Hello");
    let media_node = Node::with_binary("thumbnail", vec![1, 2, 3, 4]);

    let message_node = Node::new("message")
        .with_attribute("id", "complex-msg-123")
        .with_attribute("type", "text");

    let message_with_content = Node {
        tag: message_node.tag,
        attributes: message_node.attributes,
        content: NodeContent::Children(vec![inner_node, media_node]),
    };

    let wrapper = Node::new("action").with_attribute("type", "relay");

    let full_structure = Node {
        tag: wrapper.tag,
        attributes: wrapper.attributes,
        content: NodeContent::Children(vec![message_with_content]),
    };

    let serialized = full_structure
        .serialize()
        .expect("Should serialize complex structure");
    let deserialized = Node::parse(&serialized).expect("Should deserialize complex structure");

    assert_eq!(deserialized.tag, "action");
    let action_children = deserialized
        .children()
        .expect("Should have action children");
    assert_eq!(action_children.len(), 1);

    let message = &action_children[0];
    assert_eq!(message.tag, "message");
    let message_children = message.children().expect("Should have message children");
    assert_eq!(message_children.len(), 2);

    assert_eq!(message_children[0].tag, "body");
    assert_eq!(message_children[0].text(), Some(&"Hello".to_string()));

    assert_eq!(message_children[1].tag, "thumbnail");
    assert_eq!(message_children[1].binary(), Some(&vec![1, 2, 3, 4]));
}

#[test]
fn test_protocol_version_handling() {
    // Test version-specific attributes and elements
    let version_node = Node::new("stream:features")
        .with_attribute("version", "1.0")
        .with_attribute("xmlns:stream", "http://etherx.jabber.org/streams");

    let serialized = version_node
        .serialize()
        .expect("Should serialize version info");
    let deserialized = Node::parse(&serialized).expect("Should deserialize version info");

    assert_eq!(deserialized.tag, "stream:features");
    assert!(deserialized.has_attribute("version"));
    assert!(deserialized.has_attribute("xmlns:stream"));
}
