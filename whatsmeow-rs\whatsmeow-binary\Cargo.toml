[package]
name = "whatsmeow-binary"
version = "0.1.0"
edition = "2021"
description = "Binary node parsing and serialization for WhatsApp Web protocol"
license = "MIT"

[dependencies]
whatsmeow-types = { path = "../whatsmeow-types" }
base64 = { workspace = true }
bytes = { workspace = true }
thiserror = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_bytes = { workspace = true }

[dev-dependencies]
proptest = { workspace = true }
hex = { workspace = true }
serde_json = { workspace = true }
