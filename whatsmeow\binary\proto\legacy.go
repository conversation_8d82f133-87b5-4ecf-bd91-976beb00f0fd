// DO NOT MODIFY: Generated by generatelegacy.sh

package proto

import (
	"go.mau.fi/whatsmeow/proto/waAdv"
	"go.mau.fi/whatsmeow/proto/waCert"
	"go.mau.fi/whatsmeow/proto/waChatLockSettings"
	"go.mau.fi/whatsmeow/proto/waCommon"
	"go.mau.fi/whatsmeow/proto/waCompanionReg"
	"go.mau.fi/whatsmeow/proto/waDeviceCapabilities"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/proto/waEphemeral"
	"go.mau.fi/whatsmeow/proto/waHistorySync"
	"go.mau.fi/whatsmeow/proto/waMmsRetry"
	"go.mau.fi/whatsmeow/proto/waMsgTransport"
	"go.mau.fi/whatsmeow/proto/waQuickPromotionSurfaces"
	"go.mau.fi/whatsmeow/proto/waServerSync"
	"go.mau.fi/whatsmeow/proto/waSyncAction"
	"go.mau.fi/whatsmeow/proto/waUserPassword"
	"go.mau.fi/whatsmeow/proto/waVnameCert"
	"go.mau.fi/whatsmeow/proto/waWa6"
	"go.mau.fi/whatsmeow/proto/waWeb"
)

// Deprecated: use new packages directly
type (
	ADVEncryptionType                                                                                                   = waAdv.ADVEncryptionType
	KeepType                                                                                                            = waE2E.KeepType
	PeerDataOperationRequestType                                                                                        = waE2E.PeerDataOperationRequestType
	MediaVisibility                                                                                                     = waHistorySync.MediaVisibility
	DeviceProps_PlatformType                                                                                            = waCompanionReg.DeviceProps_PlatformType
	ImageMessage_ImageSourceType                                                                                        = waE2E.ImageMessage_ImageSourceType
	HistorySyncNotification_HistorySyncType                                                                             = waE2E.HistorySyncNotification_HistorySyncType
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_DayOfWeekType                      = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_DayOfWeekType
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_CalendarType                       = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_CalendarType
	GroupInviteMessage_GroupType                                                                                        = waE2E.GroupInviteMessage_GroupType
	ExtendedTextMessage_PreviewType                                                                                     = waE2E.ExtendedTextMessage_PreviewType
	ExtendedTextMessage_InviteLinkGroupType                                                                             = waE2E.ExtendedTextMessage_InviteLinkGroupType
	ExtendedTextMessage_FontType                                                                                        = waE2E.ExtendedTextMessage_FontType
	EventResponseMessage_EventResponseType                                                                              = waE2E.EventResponseMessage_EventResponseType
	CallLogMessage_CallType                                                                                             = waE2E.CallLogMessage_CallType
	CallLogMessage_CallOutcome                                                                                          = waE2E.CallLogMessage_CallOutcome
	ButtonsResponseMessage_Type                                                                                         = waE2E.ButtonsResponseMessage_Type
	ButtonsMessage_HeaderType                                                                                           = waE2E.ButtonsMessage_HeaderType
	ButtonsMessage_Button_Type                                                                                          = waE2E.ButtonsMessage_Button_Type
	BotFeedbackMessage_BotFeedbackKindMultiplePositive                                                                  = waE2E.BotFeedbackMessage_BotFeedbackKindMultiplePositive
	BotFeedbackMessage_BotFeedbackKindMultipleNegative                                                                  = waE2E.BotFeedbackMessage_BotFeedbackKindMultipleNegative
	BotFeedbackMessage_BotFeedbackKind                                                                                  = waE2E.BotFeedbackMessage_BotFeedbackKind
	BCallMessage_MediaType                                                                                              = waE2E.BCallMessage_MediaType
	HydratedTemplateButton_HydratedURLButton_WebviewPresentationType                                                    = waE2E.HydratedTemplateButton_HydratedURLButton_WebviewPresentationType
	DisappearingMode_Trigger                                                                                            = waE2E.DisappearingMode_Trigger
	DisappearingMode_Initiator                                                                                          = waE2E.DisappearingMode_Initiator
	ContextInfo_ExternalAdReplyInfo_MediaType                                                                           = waE2E.ContextInfo_ExternalAdReplyInfo_MediaType
	ContextInfo_AdReplyInfo_MediaType                                                                                   = waE2E.ContextInfo_AdReplyInfo_MediaType
	ForwardedNewsletterMessageInfo_ContentType                                                                          = waE2E.ContextInfo_ForwardedNewsletterMessageInfo_ContentType
	BotPluginMetadata_SearchProvider                                                                                    = waE2E.BotPluginMetadata_SearchProvider
	BotPluginMetadata_PluginType                                                                                        = waE2E.BotPluginMetadata_PluginType
	PaymentBackground_Type                                                                                              = waE2E.PaymentBackground_Type
	VideoMessage_Attribution                                                                                            = waE2E.VideoMessage_Attribution
	SecretEncryptedMessage_SecretEncType                                                                                = waE2E.SecretEncryptedMessage_SecretEncType
	ScheduledCallEditMessage_EditType                                                                                   = waE2E.ScheduledCallEditMessage_EditType
	ScheduledCallCreationMessage_CallType                                                                               = waE2E.ScheduledCallCreationMessage_CallType
	RequestWelcomeMessageMetadata_LocalChatState                                                                        = waE2E.RequestWelcomeMessageMetadata_LocalChatState
	ProtocolMessage_Type                                                                                                = waE2E.ProtocolMessage_Type
	PlaceholderMessage_PlaceholderType                                                                                  = waE2E.PlaceholderMessage_PlaceholderType
	PinInChatMessage_Type                                                                                               = waE2E.PinInChatMessage_Type
	PaymentInviteMessage_ServiceType                                                                                    = waE2E.PaymentInviteMessage_ServiceType
	OrderMessage_OrderSurface                                                                                           = waE2E.OrderMessage_OrderSurface
	OrderMessage_OrderStatus                                                                                            = waE2E.OrderMessage_OrderStatus
	ListResponseMessage_ListType                                                                                        = waE2E.ListResponseMessage_ListType
	ListMessage_ListType                                                                                                = waE2E.ListMessage_ListType
	InvoiceMessage_AttachmentType                                                                                       = waE2E.InvoiceMessage_AttachmentType
	InteractiveResponseMessage_Body_Format                                                                              = waE2E.InteractiveResponseMessage_Body_Format
	InteractiveMessage_ShopMessage_Surface                                                                              = waE2E.InteractiveMessage_ShopMessage_Surface
	PastParticipant_LeaveReason                                                                                         = waHistorySync.PastParticipant_LeaveReason
	HistorySync_HistorySyncType                                                                                         = waHistorySync.HistorySync_HistorySyncType
	HistorySync_BotAIWaitListState                                                                                      = waHistorySync.HistorySync_BotAIWaitListState
	GroupParticipant_Rank                                                                                               = waHistorySync.GroupParticipant_Rank
	Conversation_EndOfHistoryTransferType                                                                               = waHistorySync.Conversation_EndOfHistoryTransferType
	MediaRetryNotification_ResultType                                                                                   = waMmsRetry.MediaRetryNotification_ResultType
	SyncdMutation_SyncdOperation                                                                                        = waServerSync.SyncdMutation_SyncdOperation
	StatusPrivacyAction_StatusDistributionMode                                                                          = waSyncAction.StatusPrivacyAction_StatusDistributionMode
	MarketingMessageAction_MarketingMessagePrototypeType                                                                = waSyncAction.MarketingMessageAction_MarketingMessagePrototypeType
	PatchDebugData_Platform                                                                                             = waSyncAction.PatchDebugData_Platform
	CallLogRecord_SilenceReason                                                                                         = waSyncAction.CallLogRecord_SilenceReason
	CallLogRecord_CallType                                                                                              = waSyncAction.CallLogRecord_CallType
	CallLogRecord_CallResult                                                                                            = waSyncAction.CallLogRecord_CallResult
	BizIdentityInfo_VerifiedLevelValue                                                                                  = waVnameCert.BizIdentityInfo_VerifiedLevelValue
	BizIdentityInfo_HostStorageType                                                                                     = waVnameCert.BizIdentityInfo_HostStorageType
	BizIdentityInfo_ActualActorsType                                                                                    = waVnameCert.BizIdentityInfo_ActualActorsType
	BizAccountLinkInfo_HostStorageType                                                                                  = waVnameCert.BizAccountLinkInfo_HostStorageType
	BizAccountLinkInfo_AccountType                                                                                      = waVnameCert.BizAccountLinkInfo_AccountType
	ClientPayload_Product                                                                                               = waWa6.ClientPayload_Product
	ClientPayload_IOSAppExtension                                                                                       = waWa6.ClientPayload_IOSAppExtension
	ClientPayload_ConnectType                                                                                           = waWa6.ClientPayload_ConnectType
	ClientPayload_ConnectReason                                                                                         = waWa6.ClientPayload_ConnectReason
	ClientPayload_WebInfo_WebSubPlatform                                                                                = waWa6.ClientPayload_WebInfo_WebSubPlatform
	ClientPayload_UserAgent_ReleaseChannel                                                                              = waWa6.ClientPayload_UserAgent_ReleaseChannel
	ClientPayload_UserAgent_Platform                                                                                    = waWa6.ClientPayload_UserAgent_Platform
	ClientPayload_UserAgent_DeviceType                                                                                  = waWa6.ClientPayload_UserAgent_DeviceType
	ClientPayload_DNSSource_DNSResolutionMethod                                                                         = waWa6.ClientPayload_DNSSource_DNSResolutionMethod
	WebMessageInfo_StubType                                                                                             = waWeb.WebMessageInfo_StubType
	WebMessageInfo_Status                                                                                               = waWeb.WebMessageInfo_Status
	WebMessageInfo_BizPrivacyStatus                                                                                     = waWeb.WebMessageInfo_BizPrivacyStatus
	WebFeatures_Flag                                                                                                    = waWeb.WebFeatures_Flag
	PinInChat_Type                                                                                                      = waWeb.PinInChat_Type
	PaymentInfo_TxnStatus                                                                                               = waWeb.PaymentInfo_TxnStatus
	PaymentInfo_Status                                                                                                  = waWeb.PaymentInfo_Status
	PaymentInfo_Currency                                                                                                = waWeb.PaymentInfo_Currency
	QP_FilterResult                                                                                                     = waQuickPromotionSurfaces.QP_FilterResult
	QP_FilterClientNotSupportedConfig                                                                                   = waQuickPromotionSurfaces.QP_FilterClientNotSupportedConfig
	QP_ClauseType                                                                                                       = waQuickPromotionSurfaces.QP_ClauseType
	DeviceCapabilities_ChatLockSupportLevel                                                                             = waDeviceCapabilities.DeviceCapabilities_ChatLockSupportLevel
	UserPassword_Transformer                                                                                            = waUserPassword.UserPassword_Transformer
	UserPassword_Encoding                                                                                               = waUserPassword.UserPassword_Encoding
	ADVSignedKeyIndexList                                                                                               = waAdv.ADVSignedKeyIndexList
	ADVSignedDeviceIdentity                                                                                             = waAdv.ADVSignedDeviceIdentity
	ADVSignedDeviceIdentityHMAC                                                                                         = waAdv.ADVSignedDeviceIdentityHMAC
	ADVKeyIndexList                                                                                                     = waAdv.ADVKeyIndexList
	ADVDeviceIdentity                                                                                                   = waAdv.ADVDeviceIdentity
	DeviceProps                                                                                                         = waCompanionReg.DeviceProps
	InitialSecurityNotificationSettingSync                                                                              = waE2E.InitialSecurityNotificationSettingSync
	ImageMessage                                                                                                        = waE2E.ImageMessage
	HistorySyncNotification                                                                                             = waE2E.HistorySyncNotification
	HighlyStructuredMessage                                                                                             = waE2E.HighlyStructuredMessage
	GroupInviteMessage                                                                                                  = waE2E.GroupInviteMessage
	FutureProofMessage                                                                                                  = waE2E.FutureProofMessage
	ExtendedTextMessage                                                                                                 = waE2E.ExtendedTextMessage
	EventResponseMessage                                                                                                = waE2E.EventResponseMessage
	EventMessage                                                                                                        = waE2E.EventMessage
	EncReactionMessage                                                                                                  = waE2E.EncReactionMessage
	EncEventResponseMessage                                                                                             = waE2E.EncEventResponseMessage
	EncCommentMessage                                                                                                   = waE2E.EncCommentMessage
	DocumentMessage                                                                                                     = waE2E.DocumentMessage
	DeviceSentMessage                                                                                                   = waE2E.DeviceSentMessage
	DeclinePaymentRequestMessage                                                                                        = waE2E.DeclinePaymentRequestMessage
	ContactsArrayMessage                                                                                                = waE2E.ContactsArrayMessage
	ContactMessage                                                                                                      = waE2E.ContactMessage
	CommentMessage                                                                                                      = waE2E.CommentMessage
	Chat                                                                                                                = waE2E.Chat
	CancelPaymentRequestMessage                                                                                         = waE2E.CancelPaymentRequestMessage
	Call                                                                                                                = waE2E.Call
	CallLogMessage                                                                                                      = waE2E.CallLogMessage
	ButtonsResponseMessage                                                                                              = waE2E.ButtonsResponseMessage
	ButtonsResponseMessage_SelectedDisplayText                                                                          = waE2E.ButtonsResponseMessage_SelectedDisplayText
	ButtonsMessage                                                                                                      = waE2E.ButtonsMessage
	ButtonsMessage_Text                                                                                                 = waE2E.ButtonsMessage_Text
	ButtonsMessage_DocumentMessage                                                                                      = waE2E.ButtonsMessage_DocumentMessage
	ButtonsMessage_ImageMessage                                                                                         = waE2E.ButtonsMessage_ImageMessage
	ButtonsMessage_VideoMessage                                                                                         = waE2E.ButtonsMessage_VideoMessage
	ButtonsMessage_LocationMessage                                                                                      = waE2E.ButtonsMessage_LocationMessage
	BotFeedbackMessage                                                                                                  = waE2E.BotFeedbackMessage
	BCallMessage                                                                                                        = waE2E.BCallMessage
	AudioMessage                                                                                                        = waE2E.AudioMessage
	AppStateSyncKey                                                                                                     = waE2E.AppStateSyncKey
	AppStateSyncKeyShare                                                                                                = waE2E.AppStateSyncKeyShare
	AppStateSyncKeyRequest                                                                                              = waE2E.AppStateSyncKeyRequest
	AppStateSyncKeyId                                                                                                   = waE2E.AppStateSyncKeyId
	AppStateSyncKeyFingerprint                                                                                          = waE2E.AppStateSyncKeyFingerprint
	AppStateSyncKeyData                                                                                                 = waE2E.AppStateSyncKeyData
	AppStateFatalExceptionNotification                                                                                  = waE2E.AppStateFatalExceptionNotification
	MediaNotifyMessage                                                                                                  = waE2E.MediaNotifyMessage
	Location                                                                                                            = waE2E.Location
	InteractiveAnnotation                                                                                               = waE2E.InteractiveAnnotation
	InteractiveAnnotation_Location                                                                                      = waE2E.InteractiveAnnotation_Location
	InteractiveAnnotation_Newsletter                                                                                    = waE2E.InteractiveAnnotation_Newsletter
	HydratedTemplateButton                                                                                              = waE2E.HydratedTemplateButton
	HydratedTemplateButton_QuickReplyButton                                                                             = waE2E.HydratedTemplateButton_QuickReplyButton
	HydratedTemplateButton_UrlButton                                                                                    = waE2E.HydratedTemplateButton_UrlButton
	HydratedTemplateButton_CallButton                                                                                   = waE2E.HydratedTemplateButton_CallButton
	GroupMention                                                                                                        = waE2E.GroupMention
	DisappearingMode                                                                                                    = waE2E.DisappearingMode
	DeviceListMetadata                                                                                                  = waMsgTransport.DeviceListMetadata
	ContextInfo                                                                                                         = waE2E.ContextInfo
	ForwardedNewsletterMessageInfo                                                                                      = waE2E.ContextInfo_ForwardedNewsletterMessageInfo
	BotSuggestedPromptMetadata                                                                                          = waE2E.BotSuggestedPromptMetadata
	BotPluginMetadata                                                                                                   = waE2E.BotPluginMetadata
	BotMetadata                                                                                                         = waE2E.BotMetadata
	BotAvatarMetadata                                                                                                   = waE2E.BotAvatarMetadata
	ActionLink                                                                                                          = waE2E.ActionLink
	TemplateButton                                                                                                      = waE2E.TemplateButton
	TemplateButton_QuickReplyButton_                                                                                    = waE2E.TemplateButton_QuickReplyButton_
	TemplateButton_UrlButton                                                                                            = waE2E.TemplateButton_UrlButton
	TemplateButton_CallButton_                                                                                          = waE2E.TemplateButton_CallButton_
	Point                                                                                                               = waE2E.Point
	PaymentBackground                                                                                                   = waE2E.PaymentBackground
	Money                                                                                                               = waE2E.Money
	Message                                                                                                             = waE2E.Message
	MessageSecretMessage                                                                                                = waE2E.MessageSecretMessage
	MessageContextInfo                                                                                                  = waE2E.MessageContextInfo
	VideoMessage                                                                                                        = waE2E.VideoMessage
	TemplateMessage                                                                                                     = waE2E.TemplateMessage
	TemplateMessage_FourRowTemplate_                                                                                    = waE2E.TemplateMessage_FourRowTemplate_
	TemplateMessage_HydratedFourRowTemplate_                                                                            = waE2E.TemplateMessage_HydratedFourRowTemplate_
	TemplateMessage_InteractiveMessageTemplate                                                                          = waE2E.TemplateMessage_InteractiveMessageTemplate
	TemplateButtonReplyMessage                                                                                          = waE2E.TemplateButtonReplyMessage
	StickerSyncRMRMessage                                                                                               = waE2E.StickerSyncRMRMessage
	StickerMessage                                                                                                      = waE2E.StickerMessage
	SenderKeyDistributionMessage                                                                                        = waE2E.SenderKeyDistributionMessage
	SendPaymentMessage                                                                                                  = waE2E.SendPaymentMessage
	SecretEncryptedMessage                                                                                              = waE2E.SecretEncryptedMessage
	ScheduledCallEditMessage                                                                                            = waE2E.ScheduledCallEditMessage
	ScheduledCallCreationMessage                                                                                        = waE2E.ScheduledCallCreationMessage
	RequestWelcomeMessageMetadata                                                                                       = waE2E.RequestWelcomeMessageMetadata
	RequestPhoneNumberMessage                                                                                           = waE2E.RequestPhoneNumberMessage
	RequestPaymentMessage                                                                                               = waE2E.RequestPaymentMessage
	ReactionMessage                                                                                                     = waE2E.ReactionMessage
	ProtocolMessage                                                                                                     = waE2E.ProtocolMessage
	ProductMessage                                                                                                      = waE2E.ProductMessage
	PollVoteMessage                                                                                                     = waE2E.PollVoteMessage
	PollUpdateMessage                                                                                                   = waE2E.PollUpdateMessage
	PollUpdateMessageMetadata                                                                                           = waE2E.PollUpdateMessageMetadata
	PollEncValue                                                                                                        = waE2E.PollEncValue
	PollCreationMessage                                                                                                 = waE2E.PollCreationMessage
	PlaceholderMessage                                                                                                  = waE2E.PlaceholderMessage
	PinInChatMessage                                                                                                    = waE2E.PinInChatMessage
	PeerDataOperationRequestResponseMessage                                                                             = waE2E.PeerDataOperationRequestResponseMessage
	PeerDataOperationRequestMessage                                                                                     = waE2E.PeerDataOperationRequestMessage
	PaymentInviteMessage                                                                                                = waE2E.PaymentInviteMessage
	OrderMessage                                                                                                        = waE2E.OrderMessage
	NewsletterAdminInviteMessage                                                                                        = waE2E.NewsletterAdminInviteMessage
	MessageHistoryBundle                                                                                                = waE2E.MessageHistoryBundle
	LocationMessage                                                                                                     = waE2E.LocationMessage
	LiveLocationMessage                                                                                                 = waE2E.LiveLocationMessage
	ListResponseMessage                                                                                                 = waE2E.ListResponseMessage
	ListMessage                                                                                                         = waE2E.ListMessage
	KeepInChatMessage                                                                                                   = waE2E.KeepInChatMessage
	InvoiceMessage                                                                                                      = waE2E.InvoiceMessage
	InteractiveResponseMessage                                                                                          = waE2E.InteractiveResponseMessage
	InteractiveResponseMessage_NativeFlowResponseMessage_                                                               = waE2E.InteractiveResponseMessage_NativeFlowResponseMessage_
	InteractiveMessage                                                                                                  = waE2E.InteractiveMessage
	InteractiveMessage_ShopStorefrontMessage                                                                            = waE2E.InteractiveMessage_ShopStorefrontMessage
	InteractiveMessage_CollectionMessage_                                                                               = waE2E.InteractiveMessage_CollectionMessage_
	InteractiveMessage_NativeFlowMessage_                                                                               = waE2E.InteractiveMessage_NativeFlowMessage_
	InteractiveMessage_CarouselMessage_                                                                                 = waE2E.InteractiveMessage_CarouselMessage_
	EphemeralSetting                                                                                                    = waEphemeral.EphemeralSetting
	WallpaperSettings                                                                                                   = waHistorySync.WallpaperSettings
	StickerMetadata                                                                                                     = waHistorySync.StickerMetadata
	Pushname                                                                                                            = waHistorySync.Pushname
	PhoneNumberToLIDMapping                                                                                             = waHistorySync.PhoneNumberToLIDMapping
	PastParticipants                                                                                                    = waHistorySync.PastParticipants
	PastParticipant                                                                                                     = waHistorySync.PastParticipant
	NotificationSettings                                                                                                = waHistorySync.NotificationSettings
	HistorySync                                                                                                         = waHistorySync.HistorySync
	HistorySyncMsg                                                                                                      = waHistorySync.HistorySyncMsg
	GroupParticipant                                                                                                    = waHistorySync.GroupParticipant
	GlobalSettings                                                                                                      = waHistorySync.GlobalSettings
	Conversation                                                                                                        = waHistorySync.Conversation
	AvatarUserSettings                                                                                                  = waHistorySync.AvatarUserSettings
	AutoDownloadSettings                                                                                                = waHistorySync.AutoDownloadSettings
	ServerErrorReceipt                                                                                                  = waMmsRetry.ServerErrorReceipt
	MediaRetryNotification                                                                                              = waMmsRetry.MediaRetryNotification
	MessageKey                                                                                                          = waCommon.MessageKey
	SyncdVersion                                                                                                        = waServerSync.SyncdVersion
	SyncdValue                                                                                                          = waServerSync.SyncdValue
	SyncdSnapshot                                                                                                       = waServerSync.SyncdSnapshot
	SyncdRecord                                                                                                         = waServerSync.SyncdRecord
	SyncdPatch                                                                                                          = waServerSync.SyncdPatch
	SyncdMutations                                                                                                      = waServerSync.SyncdMutations
	SyncdMutation                                                                                                       = waServerSync.SyncdMutation
	SyncdIndex                                                                                                          = waServerSync.SyncdIndex
	KeyId                                                                                                               = waServerSync.KeyId
	ExternalBlobReference                                                                                               = waServerSync.ExternalBlobReference
	ExitCode                                                                                                            = waServerSync.ExitCode
	SyncActionValue                                                                                                     = waSyncAction.SyncActionValue
	WamoUserIdentifierAction                                                                                            = waSyncAction.WamoUserIdentifierAction
	UserStatusMuteAction                                                                                                = waSyncAction.UserStatusMuteAction
	UnarchiveChatsSetting                                                                                               = waSyncAction.UnarchiveChatsSetting
	TimeFormatAction                                                                                                    = waSyncAction.TimeFormatAction
	SyncActionMessage                                                                                                   = waSyncAction.SyncActionMessage
	SyncActionMessageRange                                                                                              = waSyncAction.SyncActionMessageRange
	SubscriptionAction                                                                                                  = waSyncAction.SubscriptionAction
	StickerAction                                                                                                       = waSyncAction.StickerAction
	StatusPrivacyAction                                                                                                 = waSyncAction.StatusPrivacyAction
	StarAction                                                                                                          = waSyncAction.StarAction
	SecurityNotificationSetting                                                                                         = waSyncAction.SecurityNotificationSetting
	RemoveRecentStickerAction                                                                                           = waSyncAction.RemoveRecentStickerAction
	RecentEmojiWeightsAction                                                                                            = waSyncAction.RecentEmojiWeightsAction
	QuickReplyAction                                                                                                    = waSyncAction.QuickReplyAction
	PushNameSetting                                                                                                     = waSyncAction.PushNameSetting
	PrivacySettingRelayAllCalls                                                                                         = waSyncAction.PrivacySettingRelayAllCalls
	PrivacySettingDisableLinkPreviewsAction                                                                             = waSyncAction.PrivacySettingDisableLinkPreviewsAction
	PrimaryVersionAction                                                                                                = waSyncAction.PrimaryVersionAction
	PrimaryFeature                                                                                                      = waSyncAction.PrimaryFeature
	PnForLidChatAction                                                                                                  = waSyncAction.PnForLidChatAction
	PinAction                                                                                                           = waSyncAction.PinAction
	PaymentInfoAction                                                                                                   = waSyncAction.PaymentInfoAction
	NuxAction                                                                                                           = waSyncAction.NuxAction
	MuteAction                                                                                                          = waSyncAction.MuteAction
	MarketingMessageBroadcastAction                                                                                     = waSyncAction.MarketingMessageBroadcastAction
	MarketingMessageAction                                                                                              = waSyncAction.MarketingMessageAction
	MarkChatAsReadAction                                                                                                = waSyncAction.MarkChatAsReadAction
	LockChatAction                                                                                                      = waSyncAction.LockChatAction
	LocaleSetting                                                                                                       = waSyncAction.LocaleSetting
	LabelReorderingAction                                                                                               = waSyncAction.LabelReorderingAction
	LabelEditAction                                                                                                     = waSyncAction.LabelEditAction
	LabelAssociationAction                                                                                              = waSyncAction.LabelAssociationAction
	KeyExpiration                                                                                                       = waSyncAction.KeyExpiration
	ExternalWebBetaAction                                                                                               = waSyncAction.ExternalWebBetaAction
	DeleteMessageForMeAction                                                                                            = waSyncAction.DeleteMessageForMeAction
	DeleteIndividualCallLogAction                                                                                       = waSyncAction.DeleteIndividualCallLogAction
	DeleteChatAction                                                                                                    = waSyncAction.DeleteChatAction
	CustomPaymentMethodsAction                                                                                          = waSyncAction.CustomPaymentMethodsAction
	CustomPaymentMethod                                                                                                 = waSyncAction.CustomPaymentMethod
	CustomPaymentMethodMetadata                                                                                         = waSyncAction.CustomPaymentMethodMetadata
	ContactAction                                                                                                       = waSyncAction.ContactAction
	ClearChatAction                                                                                                     = waSyncAction.ClearChatAction
	ChatAssignmentOpenedStatusAction                                                                                    = waSyncAction.ChatAssignmentOpenedStatusAction
	ChatAssignmentAction                                                                                                = waSyncAction.ChatAssignmentAction
	CallLogAction                                                                                                       = waSyncAction.CallLogAction
	BotWelcomeRequestAction                                                                                             = waSyncAction.BotWelcomeRequestAction
	ArchiveChatAction                                                                                                   = waSyncAction.ArchiveChatAction
	AndroidUnsupportedActions                                                                                           = waSyncAction.AndroidUnsupportedActions
	AgentAction                                                                                                         = waSyncAction.AgentAction
	SyncActionData                                                                                                      = waSyncAction.SyncActionData
	RecentEmojiWeight                                                                                                   = waSyncAction.RecentEmojiWeight
	PatchDebugData                                                                                                      = waSyncAction.PatchDebugData
	CallLogRecord                                                                                                       = waSyncAction.CallLogRecord
	VerifiedNameCertificate                                                                                             = waVnameCert.VerifiedNameCertificate
	LocalizedName                                                                                                       = waVnameCert.LocalizedName
	BizIdentityInfo                                                                                                     = waVnameCert.BizIdentityInfo
	BizAccountPayload                                                                                                   = waVnameCert.BizAccountPayload
	BizAccountLinkInfo                                                                                                  = waVnameCert.BizAccountLinkInfo
	HandshakeMessage                                                                                                    = waWa6.HandshakeMessage
	HandshakeServerHello                                                                                                = waWa6.HandshakeMessage_ServerHello
	HandshakeClientHello                                                                                                = waWa6.HandshakeMessage_ClientHello
	HandshakeClientFinish                                                                                               = waWa6.HandshakeMessage_ClientFinish
	ClientPayload                                                                                                       = waWa6.ClientPayload
	WebNotificationsInfo                                                                                                = waWeb.WebNotificationsInfo
	WebMessageInfo                                                                                                      = waWeb.WebMessageInfo
	WebFeatures                                                                                                         = waWeb.WebFeatures
	UserReceipt                                                                                                         = waWeb.UserReceipt
	StatusPSA                                                                                                           = waWeb.StatusPSA
	ReportingTokenInfo                                                                                                  = waWeb.ReportingTokenInfo
	Reaction                                                                                                            = waWeb.Reaction
	PremiumMessageInfo                                                                                                  = waWeb.PremiumMessageInfo
	PollUpdate                                                                                                          = waWeb.PollUpdate
	PollAdditionalMetadata                                                                                              = waWeb.PollAdditionalMetadata
	PinInChat                                                                                                           = waWeb.PinInChat
	PhotoChange                                                                                                         = waWeb.PhotoChange
	PaymentInfo                                                                                                         = waWeb.PaymentInfo
	NotificationMessageInfo                                                                                             = waWeb.NotificationMessageInfo
	MessageAddOnContextInfo                                                                                             = waWeb.MessageAddOnContextInfo
	MediaData                                                                                                           = waWeb.MediaData
	KeepInChat                                                                                                          = waWeb.KeepInChat
	EventResponse                                                                                                       = waWeb.EventResponse
	EventAdditionalMetadata                                                                                             = waWeb.EventAdditionalMetadata
	CommentMetadata                                                                                                     = waWeb.CommentMetadata
	NoiseCertificate                                                                                                    = waCert.NoiseCertificate
	CertChain                                                                                                           = waCert.CertChain
	QP                                                                                                                  = waQuickPromotionSurfaces.QP
	ChatLockSettings                                                                                                    = waChatLockSettings.ChatLockSettings
	DeviceCapabilities                                                                                                  = waDeviceCapabilities.DeviceCapabilities
	UserPassword                                                                                                        = waUserPassword.UserPassword
	DeviceProps_HistorySyncConfig                                                                                       = waCompanionReg.DeviceProps_HistorySyncConfig
	DeviceProps_AppVersion                                                                                              = waCompanionReg.DeviceProps_AppVersion
	HighlyStructuredMessage_HSMLocalizableParameter                                                                     = waE2E.HighlyStructuredMessage_HSMLocalizableParameter
	HighlyStructuredMessage_HSMLocalizableParameter_Currency                                                            = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_Currency
	HighlyStructuredMessage_HSMLocalizableParameter_DateTime                                                            = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_DateTime
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime                                                         = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_Component                                               = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_Component
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_UnixEpoch                                               = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_UnixEpoch
	HighlyStructuredMessage_HSMLocalizableParameter_HSMCurrency                                                         = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMCurrency
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeUnixEpoch                                    = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeUnixEpoch
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent                                    = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent
	CallLogMessage_CallParticipant                                                                                      = waE2E.CallLogMessage_CallParticipant
	ButtonsMessage_Button                                                                                               = waE2E.ButtonsMessage_Button
	ButtonsMessage_Button_NativeFlowInfo                                                                                = waE2E.ButtonsMessage_Button_NativeFlowInfo
	ButtonsMessage_Button_ButtonText                                                                                    = waE2E.ButtonsMessage_Button_ButtonText
	HydratedTemplateButton_HydratedURLButton                                                                            = waE2E.HydratedTemplateButton_HydratedURLButton
	HydratedTemplateButton_HydratedQuickReplyButton                                                                     = waE2E.HydratedTemplateButton_HydratedQuickReplyButton
	HydratedTemplateButton_HydratedCallButton                                                                           = waE2E.HydratedTemplateButton_HydratedCallButton
	ContextInfo_UTMInfo                                                                                                 = waE2E.ContextInfo_UTMInfo
	ContextInfo_ExternalAdReplyInfo                                                                                     = waE2E.ContextInfo_ExternalAdReplyInfo
	ContextInfo_DataSharingContext                                                                                      = waE2E.ContextInfo_DataSharingContext
	ContextInfo_BusinessMessageForwardInfo                                                                              = waE2E.ContextInfo_BusinessMessageForwardInfo
	ContextInfo_AdReplyInfo                                                                                             = waE2E.ContextInfo_AdReplyInfo
	TemplateButton_URLButton                                                                                            = waE2E.TemplateButton_URLButton
	TemplateButton_QuickReplyButton                                                                                     = waE2E.TemplateButton_QuickReplyButton
	TemplateButton_CallButton                                                                                           = waE2E.TemplateButton_CallButton
	PaymentBackground_MediaData                                                                                         = waE2E.PaymentBackground_MediaData
	TemplateMessage_HydratedFourRowTemplate                                                                             = waE2E.TemplateMessage_HydratedFourRowTemplate
	TemplateMessage_HydratedFourRowTemplate_DocumentMessage                                                             = waE2E.TemplateMessage_HydratedFourRowTemplate_DocumentMessage
	TemplateMessage_HydratedFourRowTemplate_HydratedTitleText                                                           = waE2E.TemplateMessage_HydratedFourRowTemplate_HydratedTitleText
	TemplateMessage_HydratedFourRowTemplate_ImageMessage                                                                = waE2E.TemplateMessage_HydratedFourRowTemplate_ImageMessage
	TemplateMessage_HydratedFourRowTemplate_VideoMessage                                                                = waE2E.TemplateMessage_HydratedFourRowTemplate_VideoMessage
	TemplateMessage_HydratedFourRowTemplate_LocationMessage                                                             = waE2E.TemplateMessage_HydratedFourRowTemplate_LocationMessage
	TemplateMessage_FourRowTemplate                                                                                     = waE2E.TemplateMessage_FourRowTemplate
	TemplateMessage_FourRowTemplate_DocumentMessage                                                                     = waE2E.TemplateMessage_FourRowTemplate_DocumentMessage
	TemplateMessage_FourRowTemplate_HighlyStructuredMessage                                                             = waE2E.TemplateMessage_FourRowTemplate_HighlyStructuredMessage
	TemplateMessage_FourRowTemplate_ImageMessage                                                                        = waE2E.TemplateMessage_FourRowTemplate_ImageMessage
	TemplateMessage_FourRowTemplate_VideoMessage                                                                        = waE2E.TemplateMessage_FourRowTemplate_VideoMessage
	TemplateMessage_FourRowTemplate_LocationMessage                                                                     = waE2E.TemplateMessage_FourRowTemplate_LocationMessage
	ProductMessage_ProductSnapshot                                                                                      = waE2E.ProductMessage_ProductSnapshot
	ProductMessage_CatalogSnapshot                                                                                      = waE2E.ProductMessage_CatalogSnapshot
	PollCreationMessage_Option                                                                                          = waE2E.PollCreationMessage_Option
	PeerDataOperationRequestResponseMessage_PeerDataOperationResult                                                     = waE2E.PeerDataOperationRequestResponseMessage_PeerDataOperationResult
	PeerDataOperationRequestResponseMessage_PeerDataOperationResult_PlaceholderMessageResendResponse                    = waE2E.PeerDataOperationRequestResponseMessage_PeerDataOperationResult_PlaceholderMessageResendResponse
	PeerDataOperationRequestResponseMessage_PeerDataOperationResult_LinkPreviewResponse                                 = waE2E.PeerDataOperationRequestResponseMessage_PeerDataOperationResult_LinkPreviewResponse
	PeerDataOperationRequestResponseMessage_PeerDataOperationResult_LinkPreviewResponse_LinkPreviewHighQualityThumbnail = waE2E.PeerDataOperationRequestResponseMessage_PeerDataOperationResult_LinkPreviewResponse_LinkPreviewHighQualityThumbnail
	PeerDataOperationRequestMessage_RequestUrlPreview                                                                   = waE2E.PeerDataOperationRequestMessage_RequestUrlPreview
	PeerDataOperationRequestMessage_RequestStickerReupload                                                              = waE2E.PeerDataOperationRequestMessage_RequestStickerReupload
	PeerDataOperationRequestMessage_PlaceholderMessageResendRequest                                                     = waE2E.PeerDataOperationRequestMessage_PlaceholderMessageResendRequest
	PeerDataOperationRequestMessage_HistorySyncOnDemandRequest                                                          = waE2E.PeerDataOperationRequestMessage_HistorySyncOnDemandRequest
	ListResponseMessage_SingleSelectReply                                                                               = waE2E.ListResponseMessage_SingleSelectReply
	ListMessage_Section                                                                                                 = waE2E.ListMessage_Section
	ListMessage_Row                                                                                                     = waE2E.ListMessage_Row
	ListMessage_Product                                                                                                 = waE2E.ListMessage_Product
	ListMessage_ProductSection                                                                                          = waE2E.ListMessage_ProductSection
	ListMessage_ProductListInfo                                                                                         = waE2E.ListMessage_ProductListInfo
	ListMessage_ProductListHeaderImage                                                                                  = waE2E.ListMessage_ProductListHeaderImage
	InteractiveResponseMessage_NativeFlowResponseMessage                                                                = waE2E.InteractiveResponseMessage_NativeFlowResponseMessage
	InteractiveResponseMessage_Body                                                                                     = waE2E.InteractiveResponseMessage_Body
	InteractiveMessage_NativeFlowMessage                                                                                = waE2E.InteractiveMessage_NativeFlowMessage
	InteractiveMessage_Header                                                                                           = waE2E.InteractiveMessage_Header
	InteractiveMessage_Header_DocumentMessage                                                                           = waE2E.InteractiveMessage_Header_DocumentMessage
	InteractiveMessage_Header_ImageMessage                                                                              = waE2E.InteractiveMessage_Header_ImageMessage
	InteractiveMessage_Header_JpegThumbnail                                                                             = waE2E.InteractiveMessage_Header_JPEGThumbnail
	InteractiveMessage_Header_VideoMessage                                                                              = waE2E.InteractiveMessage_Header_VideoMessage
	InteractiveMessage_Header_LocationMessage                                                                           = waE2E.InteractiveMessage_Header_LocationMessage
	InteractiveMessage_Header_ProductMessage                                                                            = waE2E.InteractiveMessage_Header_ProductMessage
	InteractiveMessage_Footer                                                                                           = waE2E.InteractiveMessage_Footer
	InteractiveMessage_CollectionMessage                                                                                = waE2E.InteractiveMessage_CollectionMessage
	InteractiveMessage_CarouselMessage                                                                                  = waE2E.InteractiveMessage_CarouselMessage
	InteractiveMessage_Body                                                                                             = waE2E.InteractiveMessage_Body
	InteractiveMessage_ShopMessage                                                                                      = waE2E.InteractiveMessage_ShopMessage
	InteractiveMessage_NativeFlowMessage_NativeFlowButton                                                               = waE2E.InteractiveMessage_NativeFlowMessage_NativeFlowButton
	CallLogRecord_ParticipantInfo                                                                                       = waSyncAction.CallLogRecord_ParticipantInfo
	VerifiedNameCertificate_Details                                                                                     = waVnameCert.VerifiedNameCertificate_Details
	ClientPayload_WebInfo                                                                                               = waWa6.ClientPayload_WebInfo
	ClientPayload_UserAgent                                                                                             = waWa6.ClientPayload_UserAgent
	ClientPayload_InteropData                                                                                           = waWa6.ClientPayload_InteropData
	ClientPayload_DevicePairingRegistrationData                                                                         = waWa6.ClientPayload_DevicePairingRegistrationData
	ClientPayload_DNSSource                                                                                             = waWa6.ClientPayload_DNSSource
	ClientPayload_WebInfo_WebdPayload                                                                                   = waWa6.ClientPayload_WebInfo_WebdPayload
	ClientPayload_UserAgent_AppVersion                                                                                  = waWa6.ClientPayload_UserAgent_AppVersion
	NoiseCertificate_Details                                                                                            = waCert.NoiseCertificate_Details
	CertChain_NoiseCertificate                                                                                          = waCert.CertChain_NoiseCertificate
	CertChain_NoiseCertificate_Details                                                                                  = waCert.CertChain_NoiseCertificate_Details
	QP_Filter                                                                                                           = waQuickPromotionSurfaces.QP_Filter
	QP_FilterParameters                                                                                                 = waQuickPromotionSurfaces.QP_FilterParameters
	QP_FilterClause                                                                                                     = waQuickPromotionSurfaces.QP_FilterClause
	UserPassword_TransformerArg                                                                                         = waUserPassword.UserPassword_TransformerArg
	UserPassword_TransformerArg_Value                                                                                   = waUserPassword.UserPassword_TransformerArg_Value
	UserPassword_TransformerArg_Value_AsBlob                                                                            = waUserPassword.UserPassword_TransformerArg_Value_AsBlob
	UserPassword_TransformerArg_Value_AsUnsignedInteger                                                                 = waUserPassword.UserPassword_TransformerArg_Value_AsUnsignedInteger
)

// Deprecated: use new packages directly
const (
	ADVEncryptionType_E2EE                                                                       = waAdv.ADVEncryptionType_E2EE
	ADVEncryptionType_HOSTED                                                                     = waAdv.ADVEncryptionType_HOSTED
	KeepType_UNKNOWN                                                                             = waE2E.KeepType_UNKNOWN_KEEP_TYPE
	KeepType_KEEP_FOR_ALL                                                                        = waE2E.KeepType_KEEP_FOR_ALL
	KeepType_UNDO_KEEP_FOR_ALL                                                                   = waE2E.KeepType_UNDO_KEEP_FOR_ALL
	PeerDataOperationRequestType_UPLOAD_STICKER                                                  = waE2E.PeerDataOperationRequestType_UPLOAD_STICKER
	PeerDataOperationRequestType_SEND_RECENT_STICKER_BOOTSTRAP                                   = waE2E.PeerDataOperationRequestType_SEND_RECENT_STICKER_BOOTSTRAP
	PeerDataOperationRequestType_GENERATE_LINK_PREVIEW                                           = waE2E.PeerDataOperationRequestType_GENERATE_LINK_PREVIEW
	PeerDataOperationRequestType_HISTORY_SYNC_ON_DEMAND                                          = waE2E.PeerDataOperationRequestType_HISTORY_SYNC_ON_DEMAND
	PeerDataOperationRequestType_PLACEHOLDER_MESSAGE_RESEND                                      = waE2E.PeerDataOperationRequestType_PLACEHOLDER_MESSAGE_RESEND
	MediaVisibility_DEFAULT                                                                      = waHistorySync.MediaVisibility_DEFAULT
	MediaVisibility_OFF                                                                          = waHistorySync.MediaVisibility_OFF
	MediaVisibility_ON                                                                           = waHistorySync.MediaVisibility_ON
	DeviceProps_UNKNOWN                                                                          = waCompanionReg.DeviceProps_UNKNOWN
	DeviceProps_CHROME                                                                           = waCompanionReg.DeviceProps_CHROME
	DeviceProps_FIREFOX                                                                          = waCompanionReg.DeviceProps_FIREFOX
	DeviceProps_IE                                                                               = waCompanionReg.DeviceProps_IE
	DeviceProps_OPERA                                                                            = waCompanionReg.DeviceProps_OPERA
	DeviceProps_SAFARI                                                                           = waCompanionReg.DeviceProps_SAFARI
	DeviceProps_EDGE                                                                             = waCompanionReg.DeviceProps_EDGE
	DeviceProps_DESKTOP                                                                          = waCompanionReg.DeviceProps_DESKTOP
	DeviceProps_IPAD                                                                             = waCompanionReg.DeviceProps_IPAD
	DeviceProps_ANDROID_TABLET                                                                   = waCompanionReg.DeviceProps_ANDROID_TABLET
	DeviceProps_OHANA                                                                            = waCompanionReg.DeviceProps_OHANA
	DeviceProps_ALOHA                                                                            = waCompanionReg.DeviceProps_ALOHA
	DeviceProps_CATALINA                                                                         = waCompanionReg.DeviceProps_CATALINA
	DeviceProps_TCL_TV                                                                           = waCompanionReg.DeviceProps_TCL_TV
	DeviceProps_IOS_PHONE                                                                        = waCompanionReg.DeviceProps_IOS_PHONE
	DeviceProps_IOS_CATALYST                                                                     = waCompanionReg.DeviceProps_IOS_CATALYST
	DeviceProps_ANDROID_PHONE                                                                    = waCompanionReg.DeviceProps_ANDROID_PHONE
	DeviceProps_ANDROID_AMBIGUOUS                                                                = waCompanionReg.DeviceProps_ANDROID_AMBIGUOUS
	DeviceProps_WEAR_OS                                                                          = waCompanionReg.DeviceProps_WEAR_OS
	DeviceProps_AR_WRIST                                                                         = waCompanionReg.DeviceProps_AR_WRIST
	DeviceProps_AR_DEVICE                                                                        = waCompanionReg.DeviceProps_AR_DEVICE
	DeviceProps_UWP                                                                              = waCompanionReg.DeviceProps_UWP
	DeviceProps_VR                                                                               = waCompanionReg.DeviceProps_VR
	ImageMessage_USER_IMAGE                                                                      = waE2E.ImageMessage_USER_IMAGE
	ImageMessage_AI_GENERATED                                                                    = waE2E.ImageMessage_AI_GENERATED
	ImageMessage_AI_MODIFIED                                                                     = waE2E.ImageMessage_AI_MODIFIED
	HistorySyncNotification_INITIAL_BOOTSTRAP                                                    = waE2E.HistorySyncNotification_INITIAL_BOOTSTRAP
	HistorySyncNotification_INITIAL_STATUS_V3                                                    = waE2E.HistorySyncNotification_INITIAL_STATUS_V3
	HistorySyncNotification_FULL                                                                 = waE2E.HistorySyncNotification_FULL
	HistorySyncNotification_RECENT                                                               = waE2E.HistorySyncNotification_RECENT
	HistorySyncNotification_PUSH_NAME                                                            = waE2E.HistorySyncNotification_PUSH_NAME
	HistorySyncNotification_NON_BLOCKING_DATA                                                    = waE2E.HistorySyncNotification_NON_BLOCKING_DATA
	HistorySyncNotification_ON_DEMAND                                                            = waE2E.HistorySyncNotification_ON_DEMAND
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_MONDAY      = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_MONDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_TUESDAY     = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_TUESDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_WEDNESDAY   = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_WEDNESDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_THURSDAY    = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_THURSDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_FRIDAY      = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_FRIDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SATURDAY    = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SATURDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SUNDAY      = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SUNDAY
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_GREGORIAN   = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_GREGORIAN
	HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SOLAR_HIJRI = waE2E.HighlyStructuredMessage_HSMLocalizableParameter_HSMDateTime_HSMDateTimeComponent_SOLAR_HIJRI
	GroupInviteMessage_DEFAULT                                                                   = waE2E.GroupInviteMessage_DEFAULT
	GroupInviteMessage_PARENT                                                                    = waE2E.GroupInviteMessage_PARENT
	ExtendedTextMessage_NONE                                                                     = waE2E.ExtendedTextMessage_NONE
	ExtendedTextMessage_VIDEO                                                                    = waE2E.ExtendedTextMessage_VIDEO
	ExtendedTextMessage_PLACEHOLDER                                                              = waE2E.ExtendedTextMessage_PLACEHOLDER
	ExtendedTextMessage_IMAGE                                                                    = waE2E.ExtendedTextMessage_IMAGE
	ExtendedTextMessage_DEFAULT                                                                  = waE2E.ExtendedTextMessage_DEFAULT
	ExtendedTextMessage_PARENT                                                                   = waE2E.ExtendedTextMessage_PARENT
	ExtendedTextMessage_SUB                                                                      = waE2E.ExtendedTextMessage_SUB
	ExtendedTextMessage_DEFAULT_SUB                                                              = waE2E.ExtendedTextMessage_DEFAULT_SUB
	ExtendedTextMessage_SYSTEM                                                                   = waE2E.ExtendedTextMessage_SYSTEM
	ExtendedTextMessage_SYSTEM_TEXT                                                              = waE2E.ExtendedTextMessage_SYSTEM_TEXT
	ExtendedTextMessage_FB_SCRIPT                                                                = waE2E.ExtendedTextMessage_FB_SCRIPT
	ExtendedTextMessage_SYSTEM_BOLD                                                              = waE2E.ExtendedTextMessage_SYSTEM_BOLD
	ExtendedTextMessage_MORNINGBREEZE_REGULAR                                                    = waE2E.ExtendedTextMessage_MORNINGBREEZE_REGULAR
	ExtendedTextMessage_CALISTOGA_REGULAR                                                        = waE2E.ExtendedTextMessage_CALISTOGA_REGULAR
	ExtendedTextMessage_EXO2_EXTRABOLD                                                           = waE2E.ExtendedTextMessage_EXO2_EXTRABOLD
	ExtendedTextMessage_COURIERPRIME_BOLD                                                        = waE2E.ExtendedTextMessage_COURIERPRIME_BOLD
	EventResponseMessage_UNKNOWN                                                                 = waE2E.EventResponseMessage_UNKNOWN
	EventResponseMessage_GOING                                                                   = waE2E.EventResponseMessage_GOING
	EventResponseMessage_NOT_GOING                                                               = waE2E.EventResponseMessage_NOT_GOING
	CallLogMessage_REGULAR                                                                       = waE2E.CallLogMessage_REGULAR
	CallLogMessage_SCHEDULED_CALL                                                                = waE2E.CallLogMessage_SCHEDULED_CALL
	CallLogMessage_VOICE_CHAT                                                                    = waE2E.CallLogMessage_VOICE_CHAT
	CallLogMessage_CONNECTED                                                                     = waE2E.CallLogMessage_CONNECTED
	CallLogMessage_MISSED                                                                        = waE2E.CallLogMessage_MISSED
	CallLogMessage_FAILED                                                                        = waE2E.CallLogMessage_FAILED
	CallLogMessage_REJECTED                                                                      = waE2E.CallLogMessage_REJECTED
	CallLogMessage_ACCEPTED_ELSEWHERE                                                            = waE2E.CallLogMessage_ACCEPTED_ELSEWHERE
	CallLogMessage_ONGOING                                                                       = waE2E.CallLogMessage_ONGOING
	CallLogMessage_SILENCED_BY_DND                                                               = waE2E.CallLogMessage_SILENCED_BY_DND
	CallLogMessage_SILENCED_UNKNOWN_CALLER                                                       = waE2E.CallLogMessage_SILENCED_UNKNOWN_CALLER
	ButtonsResponseMessage_UNKNOWN                                                               = waE2E.ButtonsResponseMessage_UNKNOWN
	ButtonsResponseMessage_DISPLAY_TEXT                                                          = waE2E.ButtonsResponseMessage_DISPLAY_TEXT
	ButtonsMessage_UNKNOWN                                                                       = waE2E.ButtonsMessage_UNKNOWN
	ButtonsMessage_EMPTY                                                                         = waE2E.ButtonsMessage_EMPTY
	ButtonsMessage_TEXT                                                                          = waE2E.ButtonsMessage_TEXT
	ButtonsMessage_DOCUMENT                                                                      = waE2E.ButtonsMessage_DOCUMENT
	ButtonsMessage_IMAGE                                                                         = waE2E.ButtonsMessage_IMAGE
	ButtonsMessage_VIDEO                                                                         = waE2E.ButtonsMessage_VIDEO
	ButtonsMessage_LOCATION                                                                      = waE2E.ButtonsMessage_LOCATION
	ButtonsMessage_Button_UNKNOWN                                                                = waE2E.ButtonsMessage_Button_UNKNOWN
	ButtonsMessage_Button_RESPONSE                                                               = waE2E.ButtonsMessage_Button_RESPONSE
	ButtonsMessage_Button_NATIVE_FLOW                                                            = waE2E.ButtonsMessage_Button_NATIVE_FLOW
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_POSITIVE_GENERIC                                    = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_POSITIVE_GENERIC
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_GENERIC                                    = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_GENERIC
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_HELPFUL                                    = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_HELPFUL
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_INTERESTING                                = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_INTERESTING
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_ACCURATE                                   = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_ACCURATE
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_SAFE                                       = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_SAFE
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_OTHER                                      = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_OTHER
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_REFUSED                                    = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_REFUSED
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_NOT_VISUALLY_APPEALING                     = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_NOT_VISUALLY_APPEALING
	BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_NOT_RELEVANT_TO_TEXT                       = waE2E.BotFeedbackMessage_BOT_FEEDBACK_MULTIPLE_NEGATIVE_NOT_RELEVANT_TO_TEXT
	BotFeedbackMessage_BOT_FEEDBACK_POSITIVE                                                     = waE2E.BotFeedbackMessage_BOT_FEEDBACK_POSITIVE
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_GENERIC                                             = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_GENERIC
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_HELPFUL                                             = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_HELPFUL
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_INTERESTING                                         = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_INTERESTING
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_ACCURATE                                            = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_ACCURATE
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_SAFE                                                = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_SAFE
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_OTHER                                               = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_OTHER
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_REFUSED                                             = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_REFUSED
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_NOT_VISUALLY_APPEALING                              = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_NOT_VISUALLY_APPEALING
	BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_NOT_RELEVANT_TO_TEXT                                = waE2E.BotFeedbackMessage_BOT_FEEDBACK_NEGATIVE_NOT_RELEVANT_TO_TEXT
	BCallMessage_UNKNOWN                                                                         = waE2E.BCallMessage_UNKNOWN
	BCallMessage_AUDIO                                                                           = waE2E.BCallMessage_AUDIO
	BCallMessage_VIDEO                                                                           = waE2E.BCallMessage_VIDEO
	HydratedTemplateButton_HydratedURLButton_FULL                                                = waE2E.HydratedTemplateButton_HydratedURLButton_FULL
	HydratedTemplateButton_HydratedURLButton_TALL                                                = waE2E.HydratedTemplateButton_HydratedURLButton_TALL
	HydratedTemplateButton_HydratedURLButton_COMPACT                                             = waE2E.HydratedTemplateButton_HydratedURLButton_COMPACT
	DisappearingMode_UNKNOWN                                                                     = waE2E.DisappearingMode_UNKNOWN
	DisappearingMode_CHAT_SETTING                                                                = waE2E.DisappearingMode_CHAT_SETTING
	DisappearingMode_ACCOUNT_SETTING                                                             = waE2E.DisappearingMode_ACCOUNT_SETTING
	DisappearingMode_BULK_CHANGE                                                                 = waE2E.DisappearingMode_BULK_CHANGE
	DisappearingMode_BIZ_SUPPORTS_FB_HOSTING                                                     = waE2E.DisappearingMode_BIZ_SUPPORTS_FB_HOSTING
	DisappearingMode_CHANGED_IN_CHAT                                                             = waE2E.DisappearingMode_CHANGED_IN_CHAT
	DisappearingMode_INITIATED_BY_ME                                                             = waE2E.DisappearingMode_INITIATED_BY_ME
	DisappearingMode_INITIATED_BY_OTHER                                                          = waE2E.DisappearingMode_INITIATED_BY_OTHER
	DisappearingMode_BIZ_UPGRADE_FB_HOSTING                                                      = waE2E.DisappearingMode_BIZ_UPGRADE_FB_HOSTING
	ContextInfo_ExternalAdReplyInfo_NONE                                                         = waE2E.ContextInfo_ExternalAdReplyInfo_NONE
	ContextInfo_ExternalAdReplyInfo_IMAGE                                                        = waE2E.ContextInfo_ExternalAdReplyInfo_IMAGE
	ContextInfo_ExternalAdReplyInfo_VIDEO                                                        = waE2E.ContextInfo_ExternalAdReplyInfo_VIDEO
	ContextInfo_AdReplyInfo_NONE                                                                 = waE2E.ContextInfo_AdReplyInfo_NONE
	ContextInfo_AdReplyInfo_IMAGE                                                                = waE2E.ContextInfo_AdReplyInfo_IMAGE
	ContextInfo_AdReplyInfo_VIDEO                                                                = waE2E.ContextInfo_AdReplyInfo_VIDEO
	ForwardedNewsletterMessageInfo_UPDATE                                                        = waE2E.ContextInfo_ForwardedNewsletterMessageInfo_UPDATE
	ForwardedNewsletterMessageInfo_UPDATE_CARD                                                   = waE2E.ContextInfo_ForwardedNewsletterMessageInfo_UPDATE_CARD
	ForwardedNewsletterMessageInfo_LINK_CARD                                                     = waE2E.ContextInfo_ForwardedNewsletterMessageInfo_LINK_CARD
	BotPluginMetadata_BING                                                                       = waE2E.BotPluginMetadata_BING
	BotPluginMetadata_GOOGLE                                                                     = waE2E.BotPluginMetadata_GOOGLE
	BotPluginMetadata_REELS                                                                      = waE2E.BotPluginMetadata_REELS
	BotPluginMetadata_SEARCH                                                                     = waE2E.BotPluginMetadata_SEARCH
	PaymentBackground_UNKNOWN                                                                    = waE2E.PaymentBackground_UNKNOWN
	PaymentBackground_DEFAULT                                                                    = waE2E.PaymentBackground_DEFAULT
	VideoMessage_NONE                                                                            = waE2E.VideoMessage_NONE
	VideoMessage_GIPHY                                                                           = waE2E.VideoMessage_GIPHY
	VideoMessage_TENOR                                                                           = waE2E.VideoMessage_TENOR
	SecretEncryptedMessage_UNKNOWN                                                               = waE2E.SecretEncryptedMessage_UNKNOWN
	SecretEncryptedMessage_EVENT_EDIT                                                            = waE2E.SecretEncryptedMessage_EVENT_EDIT
	ScheduledCallEditMessage_UNKNOWN                                                             = waE2E.ScheduledCallEditMessage_UNKNOWN
	ScheduledCallEditMessage_CANCEL                                                              = waE2E.ScheduledCallEditMessage_CANCEL
	ScheduledCallCreationMessage_UNKNOWN                                                         = waE2E.ScheduledCallCreationMessage_UNKNOWN
	ScheduledCallCreationMessage_VOICE                                                           = waE2E.ScheduledCallCreationMessage_VOICE
	ScheduledCallCreationMessage_VIDEO                                                           = waE2E.ScheduledCallCreationMessage_VIDEO
	RequestWelcomeMessageMetadata_EMPTY                                                          = waE2E.RequestWelcomeMessageMetadata_EMPTY
	RequestWelcomeMessageMetadata_NON_EMPTY                                                      = waE2E.RequestWelcomeMessageMetadata_NON_EMPTY
	ProtocolMessage_REVOKE                                                                       = waE2E.ProtocolMessage_REVOKE
	ProtocolMessage_EPHEMERAL_SETTING                                                            = waE2E.ProtocolMessage_EPHEMERAL_SETTING
	ProtocolMessage_EPHEMERAL_SYNC_RESPONSE                                                      = waE2E.ProtocolMessage_EPHEMERAL_SYNC_RESPONSE
	ProtocolMessage_HISTORY_SYNC_NOTIFICATION                                                    = waE2E.ProtocolMessage_HISTORY_SYNC_NOTIFICATION
	ProtocolMessage_APP_STATE_SYNC_KEY_SHARE                                                     = waE2E.ProtocolMessage_APP_STATE_SYNC_KEY_SHARE
	ProtocolMessage_APP_STATE_SYNC_KEY_REQUEST                                                   = waE2E.ProtocolMessage_APP_STATE_SYNC_KEY_REQUEST
	ProtocolMessage_MSG_FANOUT_BACKFILL_REQUEST                                                  = waE2E.ProtocolMessage_MSG_FANOUT_BACKFILL_REQUEST
	ProtocolMessage_INITIAL_SECURITY_NOTIFICATION_SETTING_SYNC                                   = waE2E.ProtocolMessage_INITIAL_SECURITY_NOTIFICATION_SETTING_SYNC
	ProtocolMessage_APP_STATE_FATAL_EXCEPTION_NOTIFICATION                                       = waE2E.ProtocolMessage_APP_STATE_FATAL_EXCEPTION_NOTIFICATION
	ProtocolMessage_SHARE_PHONE_NUMBER                                                           = waE2E.ProtocolMessage_SHARE_PHONE_NUMBER
	ProtocolMessage_MESSAGE_EDIT                                                                 = waE2E.ProtocolMessage_MESSAGE_EDIT
	ProtocolMessage_PEER_DATA_OPERATION_REQUEST_MESSAGE                                          = waE2E.ProtocolMessage_PEER_DATA_OPERATION_REQUEST_MESSAGE
	ProtocolMessage_PEER_DATA_OPERATION_REQUEST_RESPONSE_MESSAGE                                 = waE2E.ProtocolMessage_PEER_DATA_OPERATION_REQUEST_RESPONSE_MESSAGE
	ProtocolMessage_REQUEST_WELCOME_MESSAGE                                                      = waE2E.ProtocolMessage_REQUEST_WELCOME_MESSAGE
	ProtocolMessage_BOT_FEEDBACK_MESSAGE                                                         = waE2E.ProtocolMessage_BOT_FEEDBACK_MESSAGE
	ProtocolMessage_MEDIA_NOTIFY_MESSAGE                                                         = waE2E.ProtocolMessage_MEDIA_NOTIFY_MESSAGE
	PlaceholderMessage_MASK_LINKED_DEVICES                                                       = waE2E.PlaceholderMessage_MASK_LINKED_DEVICES
	PinInChatMessage_UNKNOWN_TYPE                                                                = waE2E.PinInChatMessage_UNKNOWN_TYPE
	PinInChatMessage_PIN_FOR_ALL                                                                 = waE2E.PinInChatMessage_PIN_FOR_ALL
	PinInChatMessage_UNPIN_FOR_ALL                                                               = waE2E.PinInChatMessage_UNPIN_FOR_ALL
	PaymentInviteMessage_UNKNOWN                                                                 = waE2E.PaymentInviteMessage_UNKNOWN
	PaymentInviteMessage_FBPAY                                                                   = waE2E.PaymentInviteMessage_FBPAY
	PaymentInviteMessage_NOVI                                                                    = waE2E.PaymentInviteMessage_NOVI
	PaymentInviteMessage_UPI                                                                     = waE2E.PaymentInviteMessage_UPI
	OrderMessage_CATALOG                                                                         = waE2E.OrderMessage_CATALOG
	OrderMessage_INQUIRY                                                                         = waE2E.OrderMessage_INQUIRY
	OrderMessage_ACCEPTED                                                                        = waE2E.OrderMessage_ACCEPTED
	OrderMessage_DECLINED                                                                        = waE2E.OrderMessage_DECLINED
	ListResponseMessage_UNKNOWN                                                                  = waE2E.ListResponseMessage_UNKNOWN
	ListResponseMessage_SINGLE_SELECT                                                            = waE2E.ListResponseMessage_SINGLE_SELECT
	ListMessage_UNKNOWN                                                                          = waE2E.ListMessage_UNKNOWN
	ListMessage_SINGLE_SELECT                                                                    = waE2E.ListMessage_SINGLE_SELECT
	ListMessage_PRODUCT_LIST                                                                     = waE2E.ListMessage_PRODUCT_LIST
	InvoiceMessage_IMAGE                                                                         = waE2E.InvoiceMessage_IMAGE
	InvoiceMessage_PDF                                                                           = waE2E.InvoiceMessage_PDF
	InteractiveResponseMessage_Body_DEFAULT                                                      = waE2E.InteractiveResponseMessage_Body_DEFAULT
	InteractiveResponseMessage_Body_EXTENSIONS_1                                                 = waE2E.InteractiveResponseMessage_Body_EXTENSIONS_1
	InteractiveMessage_ShopMessage_UNKNOWN_SURFACE                                               = waE2E.InteractiveMessage_ShopMessage_UNKNOWN_SURFACE
	InteractiveMessage_ShopMessage_FB                                                            = waE2E.InteractiveMessage_ShopMessage_FB
	InteractiveMessage_ShopMessage_IG                                                            = waE2E.InteractiveMessage_ShopMessage_IG
	InteractiveMessage_ShopMessage_WA                                                            = waE2E.InteractiveMessage_ShopMessage_WA
	PastParticipant_LEFT                                                                         = waHistorySync.PastParticipant_LEFT
	PastParticipant_REMOVED                                                                      = waHistorySync.PastParticipant_REMOVED
	HistorySync_INITIAL_BOOTSTRAP                                                                = waHistorySync.HistorySync_INITIAL_BOOTSTRAP
	HistorySync_INITIAL_STATUS_V3                                                                = waHistorySync.HistorySync_INITIAL_STATUS_V3
	HistorySync_FULL                                                                             = waHistorySync.HistorySync_FULL
	HistorySync_RECENT                                                                           = waHistorySync.HistorySync_RECENT
	HistorySync_PUSH_NAME                                                                        = waHistorySync.HistorySync_PUSH_NAME
	HistorySync_NON_BLOCKING_DATA                                                                = waHistorySync.HistorySync_NON_BLOCKING_DATA
	HistorySync_ON_DEMAND                                                                        = waHistorySync.HistorySync_ON_DEMAND
	HistorySync_IN_WAITLIST                                                                      = waHistorySync.HistorySync_IN_WAITLIST
	HistorySync_AI_AVAILABLE                                                                     = waHistorySync.HistorySync_AI_AVAILABLE
	GroupParticipant_REGULAR                                                                     = waHistorySync.GroupParticipant_REGULAR
	GroupParticipant_ADMIN                                                                       = waHistorySync.GroupParticipant_ADMIN
	GroupParticipant_SUPERADMIN                                                                  = waHistorySync.GroupParticipant_SUPERADMIN
	Conversation_COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY                                    = waHistorySync.Conversation_COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY
	Conversation_COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY                                  = waHistorySync.Conversation_COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY
	Conversation_COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY                          = waHistorySync.Conversation_COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY
	MediaRetryNotification_GENERAL_ERROR                                                         = waMmsRetry.MediaRetryNotification_GENERAL_ERROR
	MediaRetryNotification_SUCCESS                                                               = waMmsRetry.MediaRetryNotification_SUCCESS
	MediaRetryNotification_NOT_FOUND                                                             = waMmsRetry.MediaRetryNotification_NOT_FOUND
	MediaRetryNotification_DECRYPTION_ERROR                                                      = waMmsRetry.MediaRetryNotification_DECRYPTION_ERROR
	SyncdMutation_SET                                                                            = waServerSync.SyncdMutation_SET
	SyncdMutation_REMOVE                                                                         = waServerSync.SyncdMutation_REMOVE
	StatusPrivacyAction_ALLOW_LIST                                                               = waSyncAction.StatusPrivacyAction_ALLOW_LIST
	StatusPrivacyAction_DENY_LIST                                                                = waSyncAction.StatusPrivacyAction_DENY_LIST
	StatusPrivacyAction_CONTACTS                                                                 = waSyncAction.StatusPrivacyAction_CONTACTS
	MarketingMessageAction_PERSONALIZED                                                          = waSyncAction.MarketingMessageAction_PERSONALIZED
	PatchDebugData_ANDROID                                                                       = waSyncAction.PatchDebugData_ANDROID
	PatchDebugData_SMBA                                                                          = waSyncAction.PatchDebugData_SMBA
	PatchDebugData_IPHONE                                                                        = waSyncAction.PatchDebugData_IPHONE
	PatchDebugData_SMBI                                                                          = waSyncAction.PatchDebugData_SMBI
	PatchDebugData_WEB                                                                           = waSyncAction.PatchDebugData_WEB
	PatchDebugData_UWP                                                                           = waSyncAction.PatchDebugData_UWP
	PatchDebugData_DARWIN                                                                        = waSyncAction.PatchDebugData_DARWIN
	CallLogRecord_NONE                                                                           = waSyncAction.CallLogRecord_NONE
	CallLogRecord_SCHEDULED                                                                      = waSyncAction.CallLogRecord_SCHEDULED
	CallLogRecord_PRIVACY                                                                        = waSyncAction.CallLogRecord_PRIVACY
	CallLogRecord_LIGHTWEIGHT                                                                    = waSyncAction.CallLogRecord_LIGHTWEIGHT
	CallLogRecord_REGULAR                                                                        = waSyncAction.CallLogRecord_REGULAR
	CallLogRecord_SCHEDULED_CALL                                                                 = waSyncAction.CallLogRecord_SCHEDULED_CALL
	CallLogRecord_VOICE_CHAT                                                                     = waSyncAction.CallLogRecord_VOICE_CHAT
	CallLogRecord_CONNECTED                                                                      = waSyncAction.CallLogRecord_CONNECTED
	CallLogRecord_REJECTED                                                                       = waSyncAction.CallLogRecord_REJECTED
	CallLogRecord_CANCELLED                                                                      = waSyncAction.CallLogRecord_CANCELLED
	CallLogRecord_ACCEPTEDELSEWHERE                                                              = waSyncAction.CallLogRecord_ACCEPTEDELSEWHERE
	CallLogRecord_MISSED                                                                         = waSyncAction.CallLogRecord_MISSED
	CallLogRecord_INVALID                                                                        = waSyncAction.CallLogRecord_INVALID
	CallLogRecord_UNAVAILABLE                                                                    = waSyncAction.CallLogRecord_UNAVAILABLE
	CallLogRecord_UPCOMING                                                                       = waSyncAction.CallLogRecord_UPCOMING
	CallLogRecord_FAILED                                                                         = waSyncAction.CallLogRecord_FAILED
	CallLogRecord_ABANDONED                                                                      = waSyncAction.CallLogRecord_ABANDONED
	CallLogRecord_ONGOING                                                                        = waSyncAction.CallLogRecord_ONGOING
	BizIdentityInfo_UNKNOWN                                                                      = waVnameCert.BizIdentityInfo_UNKNOWN
	BizIdentityInfo_LOW                                                                          = waVnameCert.BizIdentityInfo_LOW
	BizIdentityInfo_HIGH                                                                         = waVnameCert.BizIdentityInfo_HIGH
	BizIdentityInfo_ON_PREMISE                                                                   = waVnameCert.BizIdentityInfo_ON_PREMISE
	BizIdentityInfo_FACEBOOK                                                                     = waVnameCert.BizIdentityInfo_FACEBOOK
	BizIdentityInfo_SELF                                                                         = waVnameCert.BizIdentityInfo_SELF
	BizIdentityInfo_BSP                                                                          = waVnameCert.BizIdentityInfo_BSP
	BizAccountLinkInfo_ON_PREMISE                                                                = waVnameCert.BizAccountLinkInfo_ON_PREMISE
	BizAccountLinkInfo_FACEBOOK                                                                  = waVnameCert.BizAccountLinkInfo_FACEBOOK
	BizAccountLinkInfo_ENTERPRISE                                                                = waVnameCert.BizAccountLinkInfo_ENTERPRISE
	ClientPayload_WHATSAPP                                                                       = waWa6.ClientPayload_WHATSAPP
	ClientPayload_MESSENGER                                                                      = waWa6.ClientPayload_MESSENGER
	ClientPayload_INTEROP                                                                        = waWa6.ClientPayload_INTEROP
	ClientPayload_INTEROP_MSGR                                                                   = waWa6.ClientPayload_INTEROP_MSGR
	ClientPayload_SHARE_EXTENSION                                                                = waWa6.ClientPayload_SHARE_EXTENSION
	ClientPayload_SERVICE_EXTENSION                                                              = waWa6.ClientPayload_SERVICE_EXTENSION
	ClientPayload_INTENTS_EXTENSION                                                              = waWa6.ClientPayload_INTENTS_EXTENSION
	ClientPayload_CELLULAR_UNKNOWN                                                               = waWa6.ClientPayload_CELLULAR_UNKNOWN
	ClientPayload_WIFI_UNKNOWN                                                                   = waWa6.ClientPayload_WIFI_UNKNOWN
	ClientPayload_CELLULAR_EDGE                                                                  = waWa6.ClientPayload_CELLULAR_EDGE
	ClientPayload_CELLULAR_IDEN                                                                  = waWa6.ClientPayload_CELLULAR_IDEN
	ClientPayload_CELLULAR_UMTS                                                                  = waWa6.ClientPayload_CELLULAR_UMTS
	ClientPayload_CELLULAR_EVDO                                                                  = waWa6.ClientPayload_CELLULAR_EVDO
	ClientPayload_CELLULAR_GPRS                                                                  = waWa6.ClientPayload_CELLULAR_GPRS
	ClientPayload_CELLULAR_HSDPA                                                                 = waWa6.ClientPayload_CELLULAR_HSDPA
	ClientPayload_CELLULAR_HSUPA                                                                 = waWa6.ClientPayload_CELLULAR_HSUPA
	ClientPayload_CELLULAR_HSPA                                                                  = waWa6.ClientPayload_CELLULAR_HSPA
	ClientPayload_CELLULAR_CDMA                                                                  = waWa6.ClientPayload_CELLULAR_CDMA
	ClientPayload_CELLULAR_1XRTT                                                                 = waWa6.ClientPayload_CELLULAR_1XRTT
	ClientPayload_CELLULAR_EHRPD                                                                 = waWa6.ClientPayload_CELLULAR_EHRPD
	ClientPayload_CELLULAR_LTE                                                                   = waWa6.ClientPayload_CELLULAR_LTE
	ClientPayload_CELLULAR_HSPAP                                                                 = waWa6.ClientPayload_CELLULAR_HSPAP
	ClientPayload_PUSH                                                                           = waWa6.ClientPayload_PUSH
	ClientPayload_USER_ACTIVATED                                                                 = waWa6.ClientPayload_USER_ACTIVATED
	ClientPayload_SCHEDULED                                                                      = waWa6.ClientPayload_SCHEDULED
	ClientPayload_ERROR_RECONNECT                                                                = waWa6.ClientPayload_ERROR_RECONNECT
	ClientPayload_NETWORK_SWITCH                                                                 = waWa6.ClientPayload_NETWORK_SWITCH
	ClientPayload_PING_RECONNECT                                                                 = waWa6.ClientPayload_PING_RECONNECT
	ClientPayload_UNKNOWN                                                                        = waWa6.ClientPayload_UNKNOWN
	ClientPayload_WebInfo_WEB_BROWSER                                                            = waWa6.ClientPayload_WebInfo_WEB_BROWSER
	ClientPayload_WebInfo_APP_STORE                                                              = waWa6.ClientPayload_WebInfo_APP_STORE
	ClientPayload_WebInfo_WIN_STORE                                                              = waWa6.ClientPayload_WebInfo_WIN_STORE
	ClientPayload_WebInfo_DARWIN                                                                 = waWa6.ClientPayload_WebInfo_DARWIN
	ClientPayload_WebInfo_WIN32                                                                  = waWa6.ClientPayload_WebInfo_WIN32
	ClientPayload_UserAgent_RELEASE                                                              = waWa6.ClientPayload_UserAgent_RELEASE
	ClientPayload_UserAgent_BETA                                                                 = waWa6.ClientPayload_UserAgent_BETA
	ClientPayload_UserAgent_ALPHA                                                                = waWa6.ClientPayload_UserAgent_ALPHA
	ClientPayload_UserAgent_DEBUG                                                                = waWa6.ClientPayload_UserAgent_DEBUG
	ClientPayload_UserAgent_ANDROID                                                              = waWa6.ClientPayload_UserAgent_ANDROID
	ClientPayload_UserAgent_IOS                                                                  = waWa6.ClientPayload_UserAgent_IOS
	ClientPayload_UserAgent_WINDOWS_PHONE                                                        = waWa6.ClientPayload_UserAgent_WINDOWS_PHONE
	ClientPayload_UserAgent_BLACKBERRY                                                           = waWa6.ClientPayload_UserAgent_BLACKBERRY
	ClientPayload_UserAgent_BLACKBERRYX                                                          = waWa6.ClientPayload_UserAgent_BLACKBERRYX
	ClientPayload_UserAgent_S40                                                                  = waWa6.ClientPayload_UserAgent_S40
	ClientPayload_UserAgent_S60                                                                  = waWa6.ClientPayload_UserAgent_S60
	ClientPayload_UserAgent_PYTHON_CLIENT                                                        = waWa6.ClientPayload_UserAgent_PYTHON_CLIENT
	ClientPayload_UserAgent_TIZEN                                                                = waWa6.ClientPayload_UserAgent_TIZEN
	ClientPayload_UserAgent_ENTERPRISE                                                           = waWa6.ClientPayload_UserAgent_ENTERPRISE
	ClientPayload_UserAgent_SMB_ANDROID                                                          = waWa6.ClientPayload_UserAgent_SMB_ANDROID
	ClientPayload_UserAgent_KAIOS                                                                = waWa6.ClientPayload_UserAgent_KAIOS
	ClientPayload_UserAgent_SMB_IOS                                                              = waWa6.ClientPayload_UserAgent_SMB_IOS
	ClientPayload_UserAgent_WINDOWS                                                              = waWa6.ClientPayload_UserAgent_WINDOWS
	ClientPayload_UserAgent_WEB                                                                  = waWa6.ClientPayload_UserAgent_WEB
	ClientPayload_UserAgent_PORTAL                                                               = waWa6.ClientPayload_UserAgent_PORTAL
	ClientPayload_UserAgent_GREEN_ANDROID                                                        = waWa6.ClientPayload_UserAgent_GREEN_ANDROID
	ClientPayload_UserAgent_GREEN_IPHONE                                                         = waWa6.ClientPayload_UserAgent_GREEN_IPHONE
	ClientPayload_UserAgent_BLUE_ANDROID                                                         = waWa6.ClientPayload_UserAgent_BLUE_ANDROID
	ClientPayload_UserAgent_BLUE_IPHONE                                                          = waWa6.ClientPayload_UserAgent_BLUE_IPHONE
	ClientPayload_UserAgent_FBLITE_ANDROID                                                       = waWa6.ClientPayload_UserAgent_FBLITE_ANDROID
	ClientPayload_UserAgent_MLITE_ANDROID                                                        = waWa6.ClientPayload_UserAgent_MLITE_ANDROID
	ClientPayload_UserAgent_IGLITE_ANDROID                                                       = waWa6.ClientPayload_UserAgent_IGLITE_ANDROID
	ClientPayload_UserAgent_PAGE                                                                 = waWa6.ClientPayload_UserAgent_PAGE
	ClientPayload_UserAgent_MACOS                                                                = waWa6.ClientPayload_UserAgent_MACOS
	ClientPayload_UserAgent_OCULUS_MSG                                                           = waWa6.ClientPayload_UserAgent_OCULUS_MSG
	ClientPayload_UserAgent_OCULUS_CALL                                                          = waWa6.ClientPayload_UserAgent_OCULUS_CALL
	ClientPayload_UserAgent_MILAN                                                                = waWa6.ClientPayload_UserAgent_MILAN
	ClientPayload_UserAgent_CAPI                                                                 = waWa6.ClientPayload_UserAgent_CAPI
	ClientPayload_UserAgent_WEAROS                                                               = waWa6.ClientPayload_UserAgent_WEAROS
	ClientPayload_UserAgent_ARDEVICE                                                             = waWa6.ClientPayload_UserAgent_ARDEVICE
	ClientPayload_UserAgent_VRDEVICE                                                             = waWa6.ClientPayload_UserAgent_VRDEVICE
	ClientPayload_UserAgent_BLUE_WEB                                                             = waWa6.ClientPayload_UserAgent_BLUE_WEB
	ClientPayload_UserAgent_IPAD                                                                 = waWa6.ClientPayload_UserAgent_IPAD
	ClientPayload_UserAgent_TEST                                                                 = waWa6.ClientPayload_UserAgent_TEST
	ClientPayload_UserAgent_SMART_GLASSES                                                        = waWa6.ClientPayload_UserAgent_SMART_GLASSES
	ClientPayload_UserAgent_PHONE                                                                = waWa6.ClientPayload_UserAgent_PHONE
	ClientPayload_UserAgent_TABLET                                                               = waWa6.ClientPayload_UserAgent_TABLET
	ClientPayload_UserAgent_DESKTOP                                                              = waWa6.ClientPayload_UserAgent_DESKTOP
	ClientPayload_UserAgent_WEARABLE                                                             = waWa6.ClientPayload_UserAgent_WEARABLE
	ClientPayload_UserAgent_VR                                                                   = waWa6.ClientPayload_UserAgent_VR
	ClientPayload_DNSSource_SYSTEM                                                               = waWa6.ClientPayload_DNSSource_SYSTEM
	ClientPayload_DNSSource_GOOGLE                                                               = waWa6.ClientPayload_DNSSource_GOOGLE
	ClientPayload_DNSSource_HARDCODED                                                            = waWa6.ClientPayload_DNSSource_HARDCODED
	ClientPayload_DNSSource_OVERRIDE                                                             = waWa6.ClientPayload_DNSSource_OVERRIDE
	ClientPayload_DNSSource_FALLBACK                                                             = waWa6.ClientPayload_DNSSource_FALLBACK
	WebMessageInfo_UNKNOWN                                                                       = waWeb.WebMessageInfo_UNKNOWN
	WebMessageInfo_REVOKE                                                                        = waWeb.WebMessageInfo_REVOKE
	WebMessageInfo_CIPHERTEXT                                                                    = waWeb.WebMessageInfo_CIPHERTEXT
	WebMessageInfo_FUTUREPROOF                                                                   = waWeb.WebMessageInfo_FUTUREPROOF
	WebMessageInfo_NON_VERIFIED_TRANSITION                                                       = waWeb.WebMessageInfo_NON_VERIFIED_TRANSITION
	WebMessageInfo_UNVERIFIED_TRANSITION                                                         = waWeb.WebMessageInfo_UNVERIFIED_TRANSITION
	WebMessageInfo_VERIFIED_TRANSITION                                                           = waWeb.WebMessageInfo_VERIFIED_TRANSITION
	WebMessageInfo_VERIFIED_LOW_UNKNOWN                                                          = waWeb.WebMessageInfo_VERIFIED_LOW_UNKNOWN
	WebMessageInfo_VERIFIED_HIGH                                                                 = waWeb.WebMessageInfo_VERIFIED_HIGH
	WebMessageInfo_VERIFIED_INITIAL_UNKNOWN                                                      = waWeb.WebMessageInfo_VERIFIED_INITIAL_UNKNOWN
	WebMessageInfo_VERIFIED_INITIAL_LOW                                                          = waWeb.WebMessageInfo_VERIFIED_INITIAL_LOW
	WebMessageInfo_VERIFIED_INITIAL_HIGH                                                         = waWeb.WebMessageInfo_VERIFIED_INITIAL_HIGH
	WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_NONE                                               = waWeb.WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_NONE
	WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_HIGH                                               = waWeb.WebMessageInfo_VERIFIED_TRANSITION_ANY_TO_HIGH
	WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_LOW                                               = waWeb.WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_LOW
	WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_UNKNOWN                                           = waWeb.WebMessageInfo_VERIFIED_TRANSITION_HIGH_TO_UNKNOWN
	WebMessageInfo_VERIFIED_TRANSITION_UNKNOWN_TO_LOW                                            = waWeb.WebMessageInfo_VERIFIED_TRANSITION_UNKNOWN_TO_LOW
	WebMessageInfo_VERIFIED_TRANSITION_LOW_TO_UNKNOWN                                            = waWeb.WebMessageInfo_VERIFIED_TRANSITION_LOW_TO_UNKNOWN
	WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_LOW                                               = waWeb.WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_LOW
	WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_UNKNOWN                                           = waWeb.WebMessageInfo_VERIFIED_TRANSITION_NONE_TO_UNKNOWN
	WebMessageInfo_GROUP_CREATE                                                                  = waWeb.WebMessageInfo_GROUP_CREATE
	WebMessageInfo_GROUP_CHANGE_SUBJECT                                                          = waWeb.WebMessageInfo_GROUP_CHANGE_SUBJECT
	WebMessageInfo_GROUP_CHANGE_ICON                                                             = waWeb.WebMessageInfo_GROUP_CHANGE_ICON
	WebMessageInfo_GROUP_CHANGE_INVITE_LINK                                                      = waWeb.WebMessageInfo_GROUP_CHANGE_INVITE_LINK
	WebMessageInfo_GROUP_CHANGE_DESCRIPTION                                                      = waWeb.WebMessageInfo_GROUP_CHANGE_DESCRIPTION
	WebMessageInfo_GROUP_CHANGE_RESTRICT                                                         = waWeb.WebMessageInfo_GROUP_CHANGE_RESTRICT
	WebMessageInfo_GROUP_CHANGE_ANNOUNCE                                                         = waWeb.WebMessageInfo_GROUP_CHANGE_ANNOUNCE
	WebMessageInfo_GROUP_PARTICIPANT_ADD                                                         = waWeb.WebMessageInfo_GROUP_PARTICIPANT_ADD
	WebMessageInfo_GROUP_PARTICIPANT_REMOVE                                                      = waWeb.WebMessageInfo_GROUP_PARTICIPANT_REMOVE
	WebMessageInfo_GROUP_PARTICIPANT_PROMOTE                                                     = waWeb.WebMessageInfo_GROUP_PARTICIPANT_PROMOTE
	WebMessageInfo_GROUP_PARTICIPANT_DEMOTE                                                      = waWeb.WebMessageInfo_GROUP_PARTICIPANT_DEMOTE
	WebMessageInfo_GROUP_PARTICIPANT_INVITE                                                      = waWeb.WebMessageInfo_GROUP_PARTICIPANT_INVITE
	WebMessageInfo_GROUP_PARTICIPANT_LEAVE                                                       = waWeb.WebMessageInfo_GROUP_PARTICIPANT_LEAVE
	WebMessageInfo_GROUP_PARTICIPANT_CHANGE_NUMBER                                               = waWeb.WebMessageInfo_GROUP_PARTICIPANT_CHANGE_NUMBER
	WebMessageInfo_BROADCAST_CREATE                                                              = waWeb.WebMessageInfo_BROADCAST_CREATE
	WebMessageInfo_BROADCAST_ADD                                                                 = waWeb.WebMessageInfo_BROADCAST_ADD
	WebMessageInfo_BROADCAST_REMOVE                                                              = waWeb.WebMessageInfo_BROADCAST_REMOVE
	WebMessageInfo_GENERIC_NOTIFICATION                                                          = waWeb.WebMessageInfo_GENERIC_NOTIFICATION
	WebMessageInfo_E2E_IDENTITY_CHANGED                                                          = waWeb.WebMessageInfo_E2E_IDENTITY_CHANGED
	WebMessageInfo_E2E_ENCRYPTED                                                                 = waWeb.WebMessageInfo_E2E_ENCRYPTED
	WebMessageInfo_CALL_MISSED_VOICE                                                             = waWeb.WebMessageInfo_CALL_MISSED_VOICE
	WebMessageInfo_CALL_MISSED_VIDEO                                                             = waWeb.WebMessageInfo_CALL_MISSED_VIDEO
	WebMessageInfo_INDIVIDUAL_CHANGE_NUMBER                                                      = waWeb.WebMessageInfo_INDIVIDUAL_CHANGE_NUMBER
	WebMessageInfo_GROUP_DELETE                                                                  = waWeb.WebMessageInfo_GROUP_DELETE
	WebMessageInfo_GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE                                            = waWeb.WebMessageInfo_GROUP_ANNOUNCE_MODE_MESSAGE_BOUNCE
	WebMessageInfo_CALL_MISSED_GROUP_VOICE                                                       = waWeb.WebMessageInfo_CALL_MISSED_GROUP_VOICE
	WebMessageInfo_CALL_MISSED_GROUP_VIDEO                                                       = waWeb.WebMessageInfo_CALL_MISSED_GROUP_VIDEO
	WebMessageInfo_PAYMENT_CIPHERTEXT                                                            = waWeb.WebMessageInfo_PAYMENT_CIPHERTEXT
	WebMessageInfo_PAYMENT_FUTUREPROOF                                                           = waWeb.WebMessageInfo_PAYMENT_FUTUREPROOF
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED                                      = waWeb.WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_FAILED
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED                                    = waWeb.WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUNDED
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED                               = waWeb.WebMessageInfo_PAYMENT_TRANSACTION_STATUS_UPDATE_REFUND_FAILED
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP                             = waWeb.WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_PENDING_SETUP
	WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP                      = waWeb.WebMessageInfo_PAYMENT_TRANSACTION_STATUS_RECEIVER_SUCCESS_AFTER_HICCUP
	WebMessageInfo_PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER                                         = waWeb.WebMessageInfo_PAYMENT_ACTION_ACCOUNT_SETUP_REMINDER
	WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_REMINDER                                          = waWeb.WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_REMINDER
	WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_INVITATION                                        = waWeb.WebMessageInfo_PAYMENT_ACTION_SEND_PAYMENT_INVITATION
	WebMessageInfo_PAYMENT_ACTION_REQUEST_DECLINED                                               = waWeb.WebMessageInfo_PAYMENT_ACTION_REQUEST_DECLINED
	WebMessageInfo_PAYMENT_ACTION_REQUEST_EXPIRED                                                = waWeb.WebMessageInfo_PAYMENT_ACTION_REQUEST_EXPIRED
	WebMessageInfo_PAYMENT_ACTION_REQUEST_CANCELLED                                              = waWeb.WebMessageInfo_PAYMENT_ACTION_REQUEST_CANCELLED
	WebMessageInfo_BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM                                         = waWeb.WebMessageInfo_BIZ_VERIFIED_TRANSITION_TOP_TO_BOTTOM
	WebMessageInfo_BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP                                         = waWeb.WebMessageInfo_BIZ_VERIFIED_TRANSITION_BOTTOM_TO_TOP
	WebMessageInfo_BIZ_INTRO_TOP                                                                 = waWeb.WebMessageInfo_BIZ_INTRO_TOP
	WebMessageInfo_BIZ_INTRO_BOTTOM                                                              = waWeb.WebMessageInfo_BIZ_INTRO_BOTTOM
	WebMessageInfo_BIZ_NAME_CHANGE                                                               = waWeb.WebMessageInfo_BIZ_NAME_CHANGE
	WebMessageInfo_BIZ_MOVE_TO_CONSUMER_APP                                                      = waWeb.WebMessageInfo_BIZ_MOVE_TO_CONSUMER_APP
	WebMessageInfo_BIZ_TWO_TIER_MIGRATION_TOP                                                    = waWeb.WebMessageInfo_BIZ_TWO_TIER_MIGRATION_TOP
	WebMessageInfo_BIZ_TWO_TIER_MIGRATION_BOTTOM                                                 = waWeb.WebMessageInfo_BIZ_TWO_TIER_MIGRATION_BOTTOM
	WebMessageInfo_OVERSIZED                                                                     = waWeb.WebMessageInfo_OVERSIZED
	WebMessageInfo_GROUP_CHANGE_NO_FREQUENTLY_FORWARDED                                          = waWeb.WebMessageInfo_GROUP_CHANGE_NO_FREQUENTLY_FORWARDED
	WebMessageInfo_GROUP_V4_ADD_INVITE_SENT                                                      = waWeb.WebMessageInfo_GROUP_V4_ADD_INVITE_SENT
	WebMessageInfo_GROUP_PARTICIPANT_ADD_REQUEST_JOIN                                            = waWeb.WebMessageInfo_GROUP_PARTICIPANT_ADD_REQUEST_JOIN
	WebMessageInfo_CHANGE_EPHEMERAL_SETTING                                                      = waWeb.WebMessageInfo_CHANGE_EPHEMERAL_SETTING
	WebMessageInfo_E2E_DEVICE_CHANGED                                                            = waWeb.WebMessageInfo_E2E_DEVICE_CHANGED
	WebMessageInfo_VIEWED_ONCE                                                                   = waWeb.WebMessageInfo_VIEWED_ONCE
	WebMessageInfo_E2E_ENCRYPTED_NOW                                                             = waWeb.WebMessageInfo_E2E_ENCRYPTED_NOW
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_BSP_PREMISE                                                = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_TO_BSP_PREMISE
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_FB                                                    = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_FB
	WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_PREMISE                                               = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_TO_SELF_PREMISE
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED                                                    = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED                           = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED                                                      = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED                           = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE                                          = waWeb.WebMessageInfo_BLUE_MSG_BSP_PREMISE_TO_SELF_PREMISE
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED                                               = waWeb.WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED                      = waWeb.WebMessageInfo_BLUE_MSG_BSP_PREMISE_UNVERIFIED_TO_SELF_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED                                                 = waWeb.WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED                      = waWeb.WebMessageInfo_BLUE_MSG_BSP_PREMISE_VERIFIED_TO_SELF_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED                                        = waWeb.WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED                                   = waWeb.WebMessageInfo_BLUE_MSG_CONSUMER_TO_BSP_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED                                       = waWeb.WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED                                  = waWeb.WebMessageInfo_BLUE_MSG_CONSUMER_TO_SELF_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_TO_BSP_PREMISE                                               = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_TO_BSP_PREMISE
	WebMessageInfo_BLUE_MSG_SELF_FB_TO_SELF_PREMISE                                              = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_TO_SELF_PREMISE
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED                                                   = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED                          = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_SELF_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED                                                     = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED                          = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_SELF_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE                                          = waWeb.WebMessageInfo_BLUE_MSG_SELF_PREMISE_TO_BSP_PREMISE
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_UNVERIFIED                                              = waWeb.WebMessageInfo_BLUE_MSG_SELF_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_SELF_PREMISE_VERIFIED                                                = waWeb.WebMessageInfo_BLUE_MSG_SELF_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_TO_BSP_FB                                                            = waWeb.WebMessageInfo_BLUE_MSG_TO_BSP_FB
	WebMessageInfo_BLUE_MSG_TO_CONSUMER                                                          = waWeb.WebMessageInfo_BLUE_MSG_TO_CONSUMER
	WebMessageInfo_BLUE_MSG_TO_SELF_FB                                                           = waWeb.WebMessageInfo_BLUE_MSG_TO_SELF_FB
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED                                        = waWeb.WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_FB_VERIFIED
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED                                   = waWeb.WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_BSP_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED                                       = waWeb.WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_SELF_FB_VERIFIED
	WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_VERIFIED                                               = waWeb.WebMessageInfo_BLUE_MSG_UNVERIFIED_TO_VERIFIED
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED                                        = waWeb.WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED                                   = waWeb.WebMessageInfo_BLUE_MSG_VERIFIED_TO_BSP_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED                                       = waWeb.WebMessageInfo_BLUE_MSG_VERIFIED_TO_SELF_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_VERIFIED_TO_UNVERIFIED                                               = waWeb.WebMessageInfo_BLUE_MSG_VERIFIED_TO_UNVERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED                            = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED                                = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_UNVERIFIED_TO_SELF_FB_VERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED                            = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED
	WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED                                = waWeb.WebMessageInfo_BLUE_MSG_BSP_FB_VERIFIED_TO_SELF_FB_UNVERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED                           = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_UNVERIFIED_TO_BSP_PREMISE_VERIFIED
	WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED                           = waWeb.WebMessageInfo_BLUE_MSG_SELF_FB_VERIFIED_TO_BSP_PREMISE_UNVERIFIED
	WebMessageInfo_E2E_IDENTITY_UNAVAILABLE                                                      = waWeb.WebMessageInfo_E2E_IDENTITY_UNAVAILABLE
	WebMessageInfo_GROUP_CREATING                                                                = waWeb.WebMessageInfo_GROUP_CREATING
	WebMessageInfo_GROUP_CREATE_FAILED                                                           = waWeb.WebMessageInfo_GROUP_CREATE_FAILED
	WebMessageInfo_GROUP_BOUNCED                                                                 = waWeb.WebMessageInfo_GROUP_BOUNCED
	WebMessageInfo_BLOCK_CONTACT                                                                 = waWeb.WebMessageInfo_BLOCK_CONTACT
	WebMessageInfo_EPHEMERAL_SETTING_NOT_APPLIED                                                 = waWeb.WebMessageInfo_EPHEMERAL_SETTING_NOT_APPLIED
	WebMessageInfo_SYNC_FAILED                                                                   = waWeb.WebMessageInfo_SYNC_FAILED
	WebMessageInfo_SYNCING                                                                       = waWeb.WebMessageInfo_SYNCING
	WebMessageInfo_BIZ_PRIVACY_MODE_INIT_FB                                                      = waWeb.WebMessageInfo_BIZ_PRIVACY_MODE_INIT_FB
	WebMessageInfo_BIZ_PRIVACY_MODE_INIT_BSP                                                     = waWeb.WebMessageInfo_BIZ_PRIVACY_MODE_INIT_BSP
	WebMessageInfo_BIZ_PRIVACY_MODE_TO_FB                                                        = waWeb.WebMessageInfo_BIZ_PRIVACY_MODE_TO_FB
	WebMessageInfo_BIZ_PRIVACY_MODE_TO_BSP                                                       = waWeb.WebMessageInfo_BIZ_PRIVACY_MODE_TO_BSP
	WebMessageInfo_DISAPPEARING_MODE                                                             = waWeb.WebMessageInfo_DISAPPEARING_MODE
	WebMessageInfo_E2E_DEVICE_FETCH_FAILED                                                       = waWeb.WebMessageInfo_E2E_DEVICE_FETCH_FAILED
	WebMessageInfo_ADMIN_REVOKE                                                                  = waWeb.WebMessageInfo_ADMIN_REVOKE
	WebMessageInfo_GROUP_INVITE_LINK_GROWTH_LOCKED                                               = waWeb.WebMessageInfo_GROUP_INVITE_LINK_GROWTH_LOCKED
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP                                                   = waWeb.WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP
	WebMessageInfo_COMMUNITY_LINK_SIBLING_GROUP                                                  = waWeb.WebMessageInfo_COMMUNITY_LINK_SIBLING_GROUP
	WebMessageInfo_COMMUNITY_LINK_SUB_GROUP                                                      = waWeb.WebMessageInfo_COMMUNITY_LINK_SUB_GROUP
	WebMessageInfo_COMMUNITY_UNLINK_PARENT_GROUP                                                 = waWeb.WebMessageInfo_COMMUNITY_UNLINK_PARENT_GROUP
	WebMessageInfo_COMMUNITY_UNLINK_SIBLING_GROUP                                                = waWeb.WebMessageInfo_COMMUNITY_UNLINK_SIBLING_GROUP
	WebMessageInfo_COMMUNITY_UNLINK_SUB_GROUP                                                    = waWeb.WebMessageInfo_COMMUNITY_UNLINK_SUB_GROUP
	WebMessageInfo_GROUP_PARTICIPANT_ACCEPT                                                      = waWeb.WebMessageInfo_GROUP_PARTICIPANT_ACCEPT
	WebMessageInfo_GROUP_PARTICIPANT_LINKED_GROUP_JOIN                                           = waWeb.WebMessageInfo_GROUP_PARTICIPANT_LINKED_GROUP_JOIN
	WebMessageInfo_COMMUNITY_CREATE                                                              = waWeb.WebMessageInfo_COMMUNITY_CREATE
	WebMessageInfo_EPHEMERAL_KEEP_IN_CHAT                                                        = waWeb.WebMessageInfo_EPHEMERAL_KEEP_IN_CHAT
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST                                        = waWeb.WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE                                           = waWeb.WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_MODE
	WebMessageInfo_INTEGRITY_UNLINK_PARENT_GROUP                                                 = waWeb.WebMessageInfo_INTEGRITY_UNLINK_PARENT_GROUP
	WebMessageInfo_COMMUNITY_PARTICIPANT_PROMOTE                                                 = waWeb.WebMessageInfo_COMMUNITY_PARTICIPANT_PROMOTE
	WebMessageInfo_COMMUNITY_PARTICIPANT_DEMOTE                                                  = waWeb.WebMessageInfo_COMMUNITY_PARTICIPANT_DEMOTE
	WebMessageInfo_COMMUNITY_PARENT_GROUP_DELETED                                                = waWeb.WebMessageInfo_COMMUNITY_PARENT_GROUP_DELETED
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL                               = waWeb.WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_MEMBERSHIP_APPROVAL
	WebMessageInfo_GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP                               = waWeb.WebMessageInfo_GROUP_PARTICIPANT_JOINED_GROUP_AND_PARENT_GROUP
	WebMessageInfo_MASKED_THREAD_CREATED                                                         = waWeb.WebMessageInfo_MASKED_THREAD_CREATED
	WebMessageInfo_MASKED_THREAD_UNMASKED                                                        = waWeb.WebMessageInfo_MASKED_THREAD_UNMASKED
	WebMessageInfo_BIZ_CHAT_ASSIGNMENT                                                           = waWeb.WebMessageInfo_BIZ_CHAT_ASSIGNMENT
	WebMessageInfo_CHAT_PSA                                                                      = waWeb.WebMessageInfo_CHAT_PSA
	WebMessageInfo_CHAT_POLL_CREATION_MESSAGE                                                    = waWeb.WebMessageInfo_CHAT_POLL_CREATION_MESSAGE
	WebMessageInfo_CAG_MASKED_THREAD_CREATED                                                     = waWeb.WebMessageInfo_CAG_MASKED_THREAD_CREATED
	WebMessageInfo_COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED                                        = waWeb.WebMessageInfo_COMMUNITY_PARENT_GROUP_SUBJECT_CHANGED
	WebMessageInfo_CAG_INVITE_AUTO_ADD                                                           = waWeb.WebMessageInfo_CAG_INVITE_AUTO_ADD
	WebMessageInfo_BIZ_CHAT_ASSIGNMENT_UNASSIGN                                                  = waWeb.WebMessageInfo_BIZ_CHAT_ASSIGNMENT_UNASSIGN
	WebMessageInfo_CAG_INVITE_AUTO_JOINED                                                        = waWeb.WebMessageInfo_CAG_INVITE_AUTO_JOINED
	WebMessageInfo_SCHEDULED_CALL_START_MESSAGE                                                  = waWeb.WebMessageInfo_SCHEDULED_CALL_START_MESSAGE
	WebMessageInfo_COMMUNITY_INVITE_RICH                                                         = waWeb.WebMessageInfo_COMMUNITY_INVITE_RICH
	WebMessageInfo_COMMUNITY_INVITE_AUTO_ADD_RICH                                                = waWeb.WebMessageInfo_COMMUNITY_INVITE_AUTO_ADD_RICH
	WebMessageInfo_SUB_GROUP_INVITE_RICH                                                         = waWeb.WebMessageInfo_SUB_GROUP_INVITE_RICH
	WebMessageInfo_SUB_GROUP_PARTICIPANT_ADD_RICH                                                = waWeb.WebMessageInfo_SUB_GROUP_PARTICIPANT_ADD_RICH
	WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_RICH                                              = waWeb.WebMessageInfo_COMMUNITY_LINK_PARENT_GROUP_RICH
	WebMessageInfo_COMMUNITY_PARTICIPANT_ADD_RICH                                                = waWeb.WebMessageInfo_COMMUNITY_PARTICIPANT_ADD_RICH
	WebMessageInfo_SILENCED_UNKNOWN_CALLER_AUDIO                                                 = waWeb.WebMessageInfo_SILENCED_UNKNOWN_CALLER_AUDIO
	WebMessageInfo_SILENCED_UNKNOWN_CALLER_VIDEO                                                 = waWeb.WebMessageInfo_SILENCED_UNKNOWN_CALLER_VIDEO
	WebMessageInfo_GROUP_MEMBER_ADD_MODE                                                         = waWeb.WebMessageInfo_GROUP_MEMBER_ADD_MODE
	WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD                          = waWeb.WebMessageInfo_GROUP_MEMBERSHIP_JOIN_APPROVAL_REQUEST_NON_ADMIN_ADD
	WebMessageInfo_COMMUNITY_CHANGE_DESCRIPTION                                                  = waWeb.WebMessageInfo_COMMUNITY_CHANGE_DESCRIPTION
	WebMessageInfo_SENDER_INVITE                                                                 = waWeb.WebMessageInfo_SENDER_INVITE
	WebMessageInfo_RECEIVER_INVITE                                                               = waWeb.WebMessageInfo_RECEIVER_INVITE
	WebMessageInfo_COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS                                           = waWeb.WebMessageInfo_COMMUNITY_ALLOW_MEMBER_ADDED_GROUPS
	WebMessageInfo_PINNED_MESSAGE_IN_CHAT                                                        = waWeb.WebMessageInfo_PINNED_MESSAGE_IN_CHAT
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITER                                                  = waWeb.WebMessageInfo_PAYMENT_INVITE_SETUP_INVITER
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY                                     = waWeb.WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_RECEIVE_ONLY
	WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE                                 = waWeb.WebMessageInfo_PAYMENT_INVITE_SETUP_INVITEE_SEND_AND_RECEIVE
	WebMessageInfo_LINKED_GROUP_CALL_START                                                       = waWeb.WebMessageInfo_LINKED_GROUP_CALL_START
	WebMessageInfo_REPORT_TO_ADMIN_ENABLED_STATUS                                                = waWeb.WebMessageInfo_REPORT_TO_ADMIN_ENABLED_STATUS
	WebMessageInfo_EMPTY_SUBGROUP_CREATE                                                         = waWeb.WebMessageInfo_EMPTY_SUBGROUP_CREATE
	WebMessageInfo_SCHEDULED_CALL_CANCEL                                                         = waWeb.WebMessageInfo_SCHEDULED_CALL_CANCEL
	WebMessageInfo_SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH                                        = waWeb.WebMessageInfo_SUBGROUP_ADMIN_TRIGGERED_AUTO_ADD_RICH
	WebMessageInfo_GROUP_CHANGE_RECENT_HISTORY_SHARING                                           = waWeb.WebMessageInfo_GROUP_CHANGE_RECENT_HISTORY_SHARING
	WebMessageInfo_PAID_MESSAGE_SERVER_CAMPAIGN_ID                                               = waWeb.WebMessageInfo_PAID_MESSAGE_SERVER_CAMPAIGN_ID
	WebMessageInfo_GENERAL_CHAT_CREATE                                                           = waWeb.WebMessageInfo_GENERAL_CHAT_CREATE
	WebMessageInfo_GENERAL_CHAT_ADD                                                              = waWeb.WebMessageInfo_GENERAL_CHAT_ADD
	WebMessageInfo_GENERAL_CHAT_AUTO_ADD_DISABLED                                                = waWeb.WebMessageInfo_GENERAL_CHAT_AUTO_ADD_DISABLED
	WebMessageInfo_SUGGESTED_SUBGROUP_ANNOUNCE                                                   = waWeb.WebMessageInfo_SUGGESTED_SUBGROUP_ANNOUNCE
	WebMessageInfo_BIZ_BOT_1P_MESSAGING_ENABLED                                                  = waWeb.WebMessageInfo_BIZ_BOT_1P_MESSAGING_ENABLED
	WebMessageInfo_CHANGE_USERNAME                                                               = waWeb.WebMessageInfo_CHANGE_USERNAME
	WebMessageInfo_BIZ_COEX_PRIVACY_INIT_SELF                                                    = waWeb.WebMessageInfo_BIZ_COEX_PRIVACY_INIT_SELF
	WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION_SELF                                              = waWeb.WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION_SELF
	WebMessageInfo_SUPPORT_AI_EDUCATION                                                          = waWeb.WebMessageInfo_SUPPORT_AI_EDUCATION
	WebMessageInfo_BIZ_BOT_3P_MESSAGING_ENABLED                                                  = waWeb.WebMessageInfo_BIZ_BOT_3P_MESSAGING_ENABLED
	WebMessageInfo_REMINDER_SETUP_MESSAGE                                                        = waWeb.WebMessageInfo_REMINDER_SETUP_MESSAGE
	WebMessageInfo_REMINDER_SENT_MESSAGE                                                         = waWeb.WebMessageInfo_REMINDER_SENT_MESSAGE
	WebMessageInfo_REMINDER_CANCEL_MESSAGE                                                       = waWeb.WebMessageInfo_REMINDER_CANCEL_MESSAGE
	WebMessageInfo_BIZ_COEX_PRIVACY_INIT                                                         = waWeb.WebMessageInfo_BIZ_COEX_PRIVACY_INIT
	WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION                                                   = waWeb.WebMessageInfo_BIZ_COEX_PRIVACY_TRANSITION
	WebMessageInfo_GROUP_DEACTIVATED                                                             = waWeb.WebMessageInfo_GROUP_DEACTIVATED
	WebMessageInfo_COMMUNITY_DEACTIVATE_SIBLING_GROUP                                            = waWeb.WebMessageInfo_COMMUNITY_DEACTIVATE_SIBLING_GROUP
	WebMessageInfo_ERROR                                                                         = waWeb.WebMessageInfo_ERROR
	WebMessageInfo_PENDING                                                                       = waWeb.WebMessageInfo_PENDING
	WebMessageInfo_SERVER_ACK                                                                    = waWeb.WebMessageInfo_SERVER_ACK
	WebMessageInfo_DELIVERY_ACK                                                                  = waWeb.WebMessageInfo_DELIVERY_ACK
	WebMessageInfo_READ                                                                          = waWeb.WebMessageInfo_READ
	WebMessageInfo_PLAYED                                                                        = waWeb.WebMessageInfo_PLAYED
	WebMessageInfo_E2EE                                                                          = waWeb.WebMessageInfo_E2EE
	WebMessageInfo_FB                                                                            = waWeb.WebMessageInfo_FB
	WebMessageInfo_BSP                                                                           = waWeb.WebMessageInfo_BSP
	WebMessageInfo_BSP_AND_FB                                                                    = waWeb.WebMessageInfo_BSP_AND_FB
	WebFeatures_NOT_STARTED                                                                      = waWeb.WebFeatures_NOT_STARTED
	WebFeatures_FORCE_UPGRADE                                                                    = waWeb.WebFeatures_FORCE_UPGRADE
	WebFeatures_DEVELOPMENT                                                                      = waWeb.WebFeatures_DEVELOPMENT
	WebFeatures_PRODUCTION                                                                       = waWeb.WebFeatures_PRODUCTION
	PinInChat_UNKNOWN_TYPE                                                                       = waWeb.PinInChat_UNKNOWN_TYPE
	PinInChat_PIN_FOR_ALL                                                                        = waWeb.PinInChat_PIN_FOR_ALL
	PinInChat_UNPIN_FOR_ALL                                                                      = waWeb.PinInChat_UNPIN_FOR_ALL
	PaymentInfo_UNKNOWN                                                                          = waWeb.PaymentInfo_UNKNOWN
	PaymentInfo_PENDING_SETUP                                                                    = waWeb.PaymentInfo_PENDING_SETUP
	PaymentInfo_PENDING_RECEIVER_SETUP                                                           = waWeb.PaymentInfo_PENDING_RECEIVER_SETUP
	PaymentInfo_INIT                                                                             = waWeb.PaymentInfo_INIT
	PaymentInfo_SUCCESS                                                                          = waWeb.PaymentInfo_SUCCESS
	PaymentInfo_COMPLETED                                                                        = waWeb.PaymentInfo_COMPLETED
	PaymentInfo_FAILED                                                                           = waWeb.PaymentInfo_FAILED
	PaymentInfo_FAILED_RISK                                                                      = waWeb.PaymentInfo_FAILED_RISK
	PaymentInfo_FAILED_PROCESSING                                                                = waWeb.PaymentInfo_FAILED_PROCESSING
	PaymentInfo_FAILED_RECEIVER_PROCESSING                                                       = waWeb.PaymentInfo_FAILED_RECEIVER_PROCESSING
	PaymentInfo_FAILED_DA                                                                        = waWeb.PaymentInfo_FAILED_DA
	PaymentInfo_FAILED_DA_FINAL                                                                  = waWeb.PaymentInfo_FAILED_DA_FINAL
	PaymentInfo_REFUNDED_TXN                                                                     = waWeb.PaymentInfo_REFUNDED_TXN
	PaymentInfo_REFUND_FAILED                                                                    = waWeb.PaymentInfo_REFUND_FAILED
	PaymentInfo_REFUND_FAILED_PROCESSING                                                         = waWeb.PaymentInfo_REFUND_FAILED_PROCESSING
	PaymentInfo_REFUND_FAILED_DA                                                                 = waWeb.PaymentInfo_REFUND_FAILED_DA
	PaymentInfo_EXPIRED_TXN                                                                      = waWeb.PaymentInfo_EXPIRED_TXN
	PaymentInfo_AUTH_CANCELED                                                                    = waWeb.PaymentInfo_AUTH_CANCELED
	PaymentInfo_AUTH_CANCEL_FAILED_PROCESSING                                                    = waWeb.PaymentInfo_AUTH_CANCEL_FAILED_PROCESSING
	PaymentInfo_AUTH_CANCEL_FAILED                                                               = waWeb.PaymentInfo_AUTH_CANCEL_FAILED
	PaymentInfo_COLLECT_INIT                                                                     = waWeb.PaymentInfo_COLLECT_INIT
	PaymentInfo_COLLECT_SUCCESS                                                                  = waWeb.PaymentInfo_COLLECT_SUCCESS
	PaymentInfo_COLLECT_FAILED                                                                   = waWeb.PaymentInfo_COLLECT_FAILED
	PaymentInfo_COLLECT_FAILED_RISK                                                              = waWeb.PaymentInfo_COLLECT_FAILED_RISK
	PaymentInfo_COLLECT_REJECTED                                                                 = waWeb.PaymentInfo_COLLECT_REJECTED
	PaymentInfo_COLLECT_EXPIRED                                                                  = waWeb.PaymentInfo_COLLECT_EXPIRED
	PaymentInfo_COLLECT_CANCELED                                                                 = waWeb.PaymentInfo_COLLECT_CANCELED
	PaymentInfo_COLLECT_CANCELLING                                                               = waWeb.PaymentInfo_COLLECT_CANCELLING
	PaymentInfo_IN_REVIEW                                                                        = waWeb.PaymentInfo_IN_REVIEW
	PaymentInfo_REVERSAL_SUCCESS                                                                 = waWeb.PaymentInfo_REVERSAL_SUCCESS
	PaymentInfo_REVERSAL_PENDING                                                                 = waWeb.PaymentInfo_REVERSAL_PENDING
	PaymentInfo_REFUND_PENDING                                                                   = waWeb.PaymentInfo_REFUND_PENDING
	PaymentInfo_UNKNOWN_STATUS                                                                   = waWeb.PaymentInfo_UNKNOWN_STATUS
	PaymentInfo_PROCESSING                                                                       = waWeb.PaymentInfo_PROCESSING
	PaymentInfo_SENT                                                                             = waWeb.PaymentInfo_SENT
	PaymentInfo_NEED_TO_ACCEPT                                                                   = waWeb.PaymentInfo_NEED_TO_ACCEPT
	PaymentInfo_COMPLETE                                                                         = waWeb.PaymentInfo_COMPLETE
	PaymentInfo_COULD_NOT_COMPLETE                                                               = waWeb.PaymentInfo_COULD_NOT_COMPLETE
	PaymentInfo_REFUNDED                                                                         = waWeb.PaymentInfo_REFUNDED
	PaymentInfo_EXPIRED                                                                          = waWeb.PaymentInfo_EXPIRED
	PaymentInfo_REJECTED                                                                         = waWeb.PaymentInfo_REJECTED
	PaymentInfo_CANCELLED                                                                        = waWeb.PaymentInfo_CANCELLED
	PaymentInfo_WAITING_FOR_PAYER                                                                = waWeb.PaymentInfo_WAITING_FOR_PAYER
	PaymentInfo_WAITING                                                                          = waWeb.PaymentInfo_WAITING
	PaymentInfo_UNKNOWN_CURRENCY                                                                 = waWeb.PaymentInfo_UNKNOWN_CURRENCY
	PaymentInfo_INR                                                                              = waWeb.PaymentInfo_INR
	QP_TRUE                                                                                      = waQuickPromotionSurfaces.QP_TRUE
	QP_FALSE                                                                                     = waQuickPromotionSurfaces.QP_FALSE
	QP_UNKNOWN                                                                                   = waQuickPromotionSurfaces.QP_UNKNOWN
	QP_PASS_BY_DEFAULT                                                                           = waQuickPromotionSurfaces.QP_PASS_BY_DEFAULT
	QP_FAIL_BY_DEFAULT                                                                           = waQuickPromotionSurfaces.QP_FAIL_BY_DEFAULT
	QP_AND                                                                                       = waQuickPromotionSurfaces.QP_AND
	QP_OR                                                                                        = waQuickPromotionSurfaces.QP_OR
	QP_NOR                                                                                       = waQuickPromotionSurfaces.QP_NOR
	DeviceCapabilities_NONE                                                                      = waDeviceCapabilities.DeviceCapabilities_NONE
	DeviceCapabilities_MINIMAL                                                                   = waDeviceCapabilities.DeviceCapabilities_MINIMAL
	DeviceCapabilities_FULL                                                                      = waDeviceCapabilities.DeviceCapabilities_FULL
	UserPassword_NONE                                                                            = waUserPassword.UserPassword_NONE
	UserPassword_PBKDF2_HMAC_SHA512                                                              = waUserPassword.UserPassword_PBKDF2_HMAC_SHA512
	UserPassword_PBKDF2_HMAC_SHA384                                                              = waUserPassword.UserPassword_PBKDF2_HMAC_SHA384
	UserPassword_UTF8                                                                            = waUserPassword.UserPassword_UTF8
)
