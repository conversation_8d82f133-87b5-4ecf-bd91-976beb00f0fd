//! Comprehensive error handling tests to match Go implementation behavior

use crate::error::BinaryError;
use crate::node::Node;

#[test]
fn test_truncated_data_errors() {
    // Test various truncated data scenarios
    let truncated_cases = vec![
        (vec![], "Empty data"),
        (vec![248], "LIST_8 without size"),
        (vec![248, 5], "LIST_8 with size but no data"),
        (vec![248, 3, 19], "Incomplete node (missing attributes)"),
        (vec![248, 4, 19, 8], "Node with attribute key but no value"),
        (vec![252], "BINARY_8 without size"),
        (vec![252, 5], "BINARY_8 with size but no data"),
        (vec![252, 3, 65, 66], "BINARY_8 with insufficient data"),
        (vec![253], "BINARY_20 without size"),
        (vec![253, 0, 0], "BINARY_20 incomplete size"),
        (vec![254], "BINARY_32 without size"),
        (vec![254, 0, 0, 0], "BINARY_32 incomplete size"),
    ];

    for (data, description) in truncated_cases {
        let result = Node::parse(&data);
        assert!(
            result.is_err(),
            "Should fail for {}: {:?}",
            description,
            data
        );

        match result.unwrap_err() {
            BinaryError::UnexpectedEndOfData => {} // Expected
            other => panic!(
                "Expected UnexpectedEndOfData for {}, got {:?}",
                description, other
            ),
        }
    }
}

#[test]
fn test_invalid_token_errors() {
    // Test invalid token handling
    let invalid_tokens = vec![
        vec![248, 2, 240], // Invalid single-byte token (240 doesn't exist, array has 236 entries)
        vec![248, 4, 19, 241, 19], // Invalid token in attribute key
        vec![248, 4, 19, 8, 242], // Invalid token in attribute value
    ];

    for invalid_data in invalid_tokens {
        let result = Node::parse(&invalid_data);

        // Debug: check what token 200 maps to
        if invalid_data == vec![248, 2, 200] {
            use crate::token;
            println!("Token 200 maps to: {:?}", token::get_single_token(200));
            println!("Token array length: {}", token::SINGLE_BYTE_TOKENS.len());
        }

        println!("Testing {:?}, result: {:?}", invalid_data, result);
        assert!(
            result.is_err(),
            "Should fail for invalid token: {:?}",
            invalid_data
        );
    }
}

#[test]
fn test_invalid_list_sizes() {
    // Test invalid list size scenarios
    let invalid_list_cases = vec![
        vec![248, 0, 19], // LIST_8 with size 0 but has data
        vec![248, 1, 19], // LIST_8 with size 1 (too small for valid node)
        vec![200],        // Invalid list token
    ];

    for invalid_data in invalid_list_cases {
        let result = Node::parse(&invalid_data);
        assert!(
            result.is_err(),
            "Should fail for invalid list: {:?}",
            invalid_data
        );
    }
}

#[test]
fn test_invalid_packed_strings() {
    // Test invalid packed string scenarios
    let invalid_packed = vec![
        vec![255, 129],    // NIBBLE_8 with odd length flag but no data
        vec![255, 1, 255], // NIBBLE_8 with invalid nibble value
        vec![251, 129],    // HEX_8 with odd length flag but no data
        vec![251, 1, 255], // HEX_8 with invalid hex value (15 is valid as terminator)
    ];

    for invalid_data in invalid_packed {
        let result = Node::parse(&invalid_data);
        // Some of these might succeed with empty strings, which is acceptable
        if result.is_ok() {
            let node = result.unwrap();
            // Should at least parse to something reasonable
            assert!(!node.tag.is_empty() || node.text().is_some());
        }
    }
}

#[test]
fn test_invalid_jid_structures() {
    // Test invalid JID pair structures
    let invalid_jid_cases = vec![
        vec![248, 3, 19, 250],    // JID_PAIR without user/server data
        vec![248, 4, 19, 250, 0], // JID_PAIR with only user, no server
    ];

    for invalid_data in invalid_jid_cases {
        let result = Node::parse(&invalid_data);
        assert!(
            result.is_err(),
            "Should fail for invalid JID: {:?}",
            invalid_data
        );
    }
}

#[test]
fn test_oversized_data_handling() {
    // Test handling of data that claims to be larger than available
    let oversized_cases = vec![
        vec![252, 255],        // BINARY_8 claims 255 bytes but has none
        vec![253, 1, 0, 0],    // BINARY_20 claims 65536 bytes but has none
        vec![254, 0, 1, 0, 0], // BINARY_32 claims 65536 bytes but has none
    ];

    for oversized_data in oversized_cases {
        let result = Node::parse(&oversized_data);
        assert!(
            result.is_err(),
            "Should fail for oversized claim: {:?}",
            oversized_data
        );

        match result.unwrap_err() {
            BinaryError::UnexpectedEndOfData => {} // Expected
            other => panic!(
                "Expected UnexpectedEndOfData for oversized data, got {:?}",
                other
            ),
        }
    }
}

#[test]
fn test_circular_reference_prevention() {
    // Test that we don't get into infinite loops with malformed data
    // This is more about ensuring the parser doesn't hang

    let potentially_circular = vec![
        vec![248, 3, 19, 248, 3, 19, 248],  // Nested LIST_8 structures
        vec![249, 0, 1, 19, 249, 0, 1, 19], // Nested LIST_16 structures
    ];

    for data in potentially_circular {
        let result = Node::parse(&data);
        // Should either succeed or fail quickly, not hang
        // The exact result doesn't matter as much as not hanging
        if result.is_ok() {}
    }
}

#[test]
fn test_memory_exhaustion_prevention() {
    // Test that we don't allocate excessive memory for malformed size claims
    let memory_exhaustion_cases = vec![
        vec![248, 255],          // Claims to have 255 list items
        vec![249, 255, 255],     // Claims to have 65535 list items
        vec![252, 255],          // Claims 255 bytes of binary data
        vec![253, 15, 255, 255], // Claims ~1MB of binary data
    ];

    for data in memory_exhaustion_cases {
        let result = Node::parse(&data);
        // Should fail gracefully without allocating excessive memory
        assert!(
            result.is_err(),
            "Should fail for memory exhaustion attempt: {:?}",
            data
        );
    }
}

#[test]
fn test_invalid_utf8_handling() {
    // Test handling of invalid UTF-8 sequences in string contexts
    let invalid_utf8_cases = vec![
        vec![248, 3, 19, 252, 4, 0xFF, 0xFE, 0xFD, 0xFC], // Invalid UTF-8 in content
        vec![248, 5, 252, 4, 0xFF, 0xFE, 0xFD, 0xFC, 19, 19], // Invalid UTF-8 in attribute key
    ];

    for invalid_utf8_data in invalid_utf8_cases {
        let result = Node::parse(&invalid_utf8_data);
        // Should either parse as binary or fail gracefully
        match result {
            Ok(node) => {
                // If it succeeds, invalid UTF-8 should be handled as binary
                if let Some(binary_data) = node.binary() {
                    assert!(!binary_data.is_empty());
                }
            }
            Err(_) => {
                // Failing is also acceptable for invalid UTF-8
            }
        }
    }
}

#[test]
fn test_double_byte_token_errors() {
    // Test invalid double-byte token scenarios
    let invalid_double_byte = vec![
        vec![248, 4, 19, 236, 255], // DICTIONARY_0 with invalid index
        vec![248, 4, 19, 237, 255], // DICTIONARY_1 with invalid index
        vec![248, 4, 19, 238, 255], // DICTIONARY_2 with invalid index
        vec![248, 4, 19, 239, 255], // DICTIONARY_3 with invalid index
        vec![248, 3, 19, 236],      // DICTIONARY_0 without index
    ];

    for invalid_data in invalid_double_byte {
        let result = Node::parse(&invalid_data);
        assert!(
            result.is_err(),
            "Should fail for invalid double-byte token: {:?}",
            invalid_data
        );
    }
}

#[test]
fn test_attribute_count_mismatch() {
    // Test scenarios where attribute count doesn't match list size
    let mismatched_cases = vec![
        vec![248, 4, 19, 8],        // Claims 4 items but only has tag + 1 attribute key
        vec![248, 6, 19, 8, 19, 8], // Claims 6 items but has tag + 2 keys (missing values)
    ];

    for mismatched_data in mismatched_cases {
        let result = Node::parse(&mismatched_data);
        assert!(
            result.is_err(),
            "Should fail for attribute count mismatch: {:?}",
            mismatched_data
        );
    }
}

#[test]
fn test_nested_error_propagation() {
    // Test that errors in nested structures are properly propagated
    let nested_error_cases = vec![
        vec![248, 3, 19, 248, 1],        // Valid outer, invalid inner list
        vec![248, 5, 19, 8, 19, 248, 1], // Valid node with invalid child
    ];

    for nested_error_data in nested_error_cases {
        let result = Node::parse(&nested_error_data);
        assert!(
            result.is_err(),
            "Should propagate nested errors: {:?}",
            nested_error_data
        );
    }
}

#[test]
fn test_serialization_error_conditions() {
    // Test conditions that should cause serialization errors

    // Empty tag should cause error
    let empty_tag_node = Node::new("");
    let result = empty_tag_node.serialize();
    assert!(
        result.is_err(),
        "Empty tag should cause serialization error"
    );

    // Very large data should still work (testing limits)
    let large_data = vec![0xAA; 1000000]; // 1MB
    let large_node = Node::with_binary("test", large_data);
    let result = large_node.serialize();
    // This should succeed (testing that we don't have artificial limits)
    assert!(result.is_ok(), "Large data should serialize successfully");
}

#[test]
fn test_recovery_from_partial_errors() {
    // Test that parser can recover from partial errors in some cases

    // Valid data followed by invalid data
    let mixed_data = vec![
        248, 2, 19, // Valid minimal node
        255, 255, 255, // Invalid trailing data
    ];

    let result = Node::parse(&mixed_data);
    // Should fail because of trailing data, but this tests the parser's behavior
    match result {
        Ok(_) => panic!("Should not succeed with trailing invalid data"),
        Err(e) => {
            // Should get a specific error about leftover data or invalid format
            match e {
                BinaryError::InvalidNodeFormat(_)
                | BinaryError::InvalidTag(_)
                | BinaryError::DeserializationFailed(_)
                | BinaryError::UnexpectedEndOfData => {} // Expected - UnexpectedEndOfData is valid when trying to read more data than available
                other => panic!("Unexpected error type: {:?}", other),
            }
        }
    }
}

#[test]
fn test_error_message_quality() {
    // Test that error messages are informative
    let error_cases = vec![
        (vec![], "empty data"),
        (vec![255], "invalid token"),
        (vec![248, 0], "invalid list size"),
    ];

    for (data, expected_context) in error_cases {
        let result = Node::parse(&data);
        assert!(result.is_err(), "Should fail for {}", expected_context);

        let error = result.unwrap_err();
        let error_string = format!("{:?}", error);
        // Error should contain some useful information
        assert!(
            !error_string.is_empty(),
            "Error message should not be empty for {}",
            expected_context
        );
    }
}

#[test]
fn test_concurrent_parsing_safety() {
    // Test that parsing is safe for concurrent use (no shared mutable state)
    use std::sync::Arc;
    use std::thread;

    let valid_data = Arc::new(vec![248, 2, 19, 252, 0]); // Simple valid node: message with empty content
    let invalid_data = Arc::new(vec![255, 255]); // Invalid data

    let mut handles = vec![];

    // Spawn multiple threads parsing the same data
    for i in 0..10 {
        let valid_data_clone = Arc::clone(&valid_data);
        let invalid_data_clone = Arc::clone(&invalid_data);

        let handle = thread::spawn(move || {
            // Each thread should get consistent results
            let valid_result = Node::parse(&valid_data_clone);
            let invalid_result = Node::parse(&invalid_data_clone);

            (i, valid_result.is_ok(), invalid_result.is_err())
        });

        handles.push(handle);
    }

    // Collect results
    for handle in handles {
        let (thread_id, valid_ok, invalid_err) = handle.join().unwrap();
        assert!(
            valid_ok,
            "Thread {} should parse valid data successfully",
            thread_id
        );
        assert!(
            invalid_err,
            "Thread {} should fail on invalid data",
            thread_id
        );
    }
}
