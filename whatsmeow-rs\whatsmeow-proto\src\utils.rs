//! Utility functions for protocol buffer operations

use crate::error::{ProtoError, ProtoResult};
use prost::Message;
use std::io::Cursor;

/// Serialize a protocol buffer message to bytes
pub fn serialize_proto<T: Message>(message: &T) -> ProtoResult<Vec<u8>> {
    let mut buf = Vec::new();
    message
        .encode(&mut buf)
        .map_err(|e| ProtoError::SerializationFailed(e.to_string()))?;
    Ok(buf)
}

/// Deserialize bytes to a protocol buffer message
pub fn deserialize_proto<T: Message + Default>(bytes: &[u8]) -> ProtoResult<T> {
    T::decode(bytes).map_err(|e| ProtoError::DeserializationFailed(e.to_string()))
}

/// Serialize a protocol buffer message to bytes with length prefix
pub fn serialize_proto_with_length<T: Message>(message: &T) -> ProtoResult<Vec<u8>> {
    let serialized = serialize_proto(message)?;
    let length = serialized.len() as u32;

    let mut result = Vec::with_capacity(4 + serialized.len());
    result.extend_from_slice(&length.to_be_bytes());
    result.extend_from_slice(&serialized);

    Ok(result)
}

/// Deserialize bytes with length prefix to a protocol buffer message
pub fn deserialize_proto_with_length<T: Message + Default>(bytes: &[u8]) -> ProtoResult<T> {
    if bytes.len() < 4 {
        return Err(ProtoError::BufferTooSmall {
            expected: 4,
            actual: bytes.len(),
        });
    }

    let length = u32::from_be_bytes([bytes[0], bytes[1], bytes[2], bytes[3]]) as usize;

    if bytes.len() < 4 + length {
        return Err(ProtoError::BufferTooSmall {
            expected: 4 + length,
            actual: bytes.len(),
        });
    }

    deserialize_proto(&bytes[4..4 + length])
}

/// Check if a protocol buffer message is valid (can be serialized and deserialized)
pub fn validate_proto_message<T: Message + Default + Clone>(message: &T) -> ProtoResult<()> {
    let serialized = serialize_proto(message)?;
    let _deserialized: T = deserialize_proto(&serialized)?;
    Ok(())
}

/// Get the serialized size of a protocol buffer message
pub fn get_proto_size<T: Message>(message: &T) -> usize {
    message.encoded_len()
}

/// Create a cursor from protocol buffer bytes for streaming operations
pub fn create_proto_cursor(bytes: Vec<u8>) -> Cursor<Vec<u8>> {
    Cursor::new(bytes)
}

/// Merge two protocol buffer messages of the same type
pub fn merge_proto_messages<T: Message + Default>(base: &mut T, other: &T) -> ProtoResult<()> {
    let other_bytes = serialize_proto(other)?;
    base.merge(&*other_bytes)
        .map_err(|e| ProtoError::DeserializationFailed(format!("Merge failed: {}", e)))?;
    Ok(())
}

/// Convert a protocol buffer message to JSON string (if serde feature is enabled)
#[cfg(feature = "serde")]
pub fn proto_to_json<T: serde::Serialize>(message: &T) -> ProtoResult<String> {
    serde_json::to_string(message)
        .map_err(|e| ProtoError::SerializationFailed(format!("JSON serialization failed: {}", e)))
}

/// Convert JSON string to a protocol buffer message (if serde feature is enabled)
#[cfg(feature = "serde")]
pub fn json_to_proto<T: serde::de::DeserializeOwned>(json: &str) -> ProtoResult<T> {
    serde_json::from_str(json).map_err(|e| {
        ProtoError::DeserializationFailed(format!("JSON deserialization failed: {}", e))
    })
}

/// Pretty print a protocol buffer message (if debug feature is enabled)
#[cfg(feature = "debug")]
pub fn debug_proto_message<T: std::fmt::Debug>(message: &T) -> String {
    format!("{:#?}", message)
}

/// Compress protocol buffer bytes using gzip
#[cfg(feature = "compression")]
pub fn compress_proto_bytes(bytes: &[u8]) -> ProtoResult<Vec<u8>> {
    use flate2::write::GzEncoder;
    use flate2::Compression;
    use std::io::Write;

    let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
    encoder
        .write_all(bytes)
        .map_err(|e| ProtoError::SerializationFailed(format!("Compression failed: {}", e)))?;
    encoder
        .finish()
        .map_err(|e| ProtoError::SerializationFailed(format!("Compression finish failed: {}", e)))
}

/// Decompress gzipped protocol buffer bytes
#[cfg(feature = "compression")]
pub fn decompress_proto_bytes(compressed: &[u8]) -> ProtoResult<Vec<u8>> {
    use flate2::read::GzDecoder;
    use std::io::Read;

    let mut decoder = GzDecoder::new(compressed);
    let mut decompressed = Vec::new();
    decoder
        .read_to_end(&mut decompressed)
        .map_err(|e| ProtoError::DeserializationFailed(format!("Decompression failed: {}", e)))?;
    Ok(decompressed)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::wa_proto;

    #[test]
    fn test_serialize_deserialize_proto() {
        let original = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test123".to_string()),
            participant: None,
        };

        let serialized = serialize_proto(&original).unwrap();
        let deserialized: wa_proto::MessageKey = deserialize_proto(&serialized).unwrap();

        assert_eq!(original.remote_jid, deserialized.remote_jid);
        assert_eq!(original.from_me, deserialized.from_me);
        assert_eq!(original.id, deserialized.id);
        assert_eq!(original.participant, deserialized.participant);
    }

    #[test]
    fn test_serialize_deserialize_with_length() {
        let original = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(false),
            id: Some("test456".to_string()),
            participant: Some("<EMAIL>".to_string()),
        };

        let serialized = serialize_proto_with_length(&original).unwrap();
        let deserialized: wa_proto::MessageKey =
            deserialize_proto_with_length(&serialized).unwrap();

        assert_eq!(original.remote_jid, deserialized.remote_jid);
        assert_eq!(original.from_me, deserialized.from_me);
        assert_eq!(original.id, deserialized.id);
        assert_eq!(original.participant, deserialized.participant);
    }

    #[test]
    fn test_deserialize_with_length_buffer_too_small() {
        let short_buffer = vec![0, 0]; // Less than 4 bytes
        let result: Result<wa_proto::MessageKey, ProtoError> =
            deserialize_proto_with_length(&short_buffer);

        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::BufferTooSmall { .. }
        ));
    }

    #[test]
    fn test_deserialize_with_length_insufficient_data() {
        let buffer = vec![0, 0, 0, 10]; // Claims 10 bytes but provides none
        let result: Result<wa_proto::MessageKey, ProtoError> =
            deserialize_proto_with_length(&buffer);

        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::BufferTooSmall { .. }
        ));
    }

    #[test]
    fn test_validate_proto_message() {
        let valid_message = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test789".to_string()),
            participant: None,
        };

        let result = validate_proto_message(&valid_message);
        assert!(result.is_ok());
    }

    #[test]
    fn test_get_proto_size() {
        let message = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test123".to_string()),
            participant: None,
        };

        let size = get_proto_size(&message);
        assert!(size > 0);

        // Verify size matches actual serialized length
        let serialized = serialize_proto(&message).unwrap();
        assert_eq!(size, serialized.len());
    }

    #[test]
    fn test_create_proto_cursor() {
        let data = vec![1, 2, 3, 4, 5];
        let cursor = create_proto_cursor(data.clone());
        assert_eq!(cursor.get_ref(), &data);
    }

    #[test]
    fn test_merge_proto_messages() {
        let mut base = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: None,
            id: None,
            participant: None,
        };

        let other = wa_proto::MessageKey {
            remote_jid: None,
            from_me: Some(true),
            id: Some("merged_id".to_string()),
            participant: Some("<EMAIL>".to_string()),
        };

        let result = merge_proto_messages(&mut base, &other);
        assert!(result.is_ok());

        // Base should retain its original remote_jid and gain other fields
        assert_eq!(base.remote_jid, Some("<EMAIL>".to_string()));
        assert_eq!(base.from_me, Some(true));
        assert_eq!(base.id, Some("merged_id".to_string()));
        assert_eq!(
            base.participant,
            Some("<EMAIL>".to_string())
        );
    }

    #[cfg(feature = "serde")]
    #[test]
    fn test_proto_json_conversion() {
        let message = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("json_test".to_string()),
            participant: None,
        };

        let json = proto_to_json(&message).unwrap();
        let deserialized: wa_proto::MessageKey = json_to_proto(&json).unwrap();

        assert_eq!(message.remote_jid, deserialized.remote_jid);
        assert_eq!(message.from_me, deserialized.from_me);
        assert_eq!(message.id, deserialized.id);
        assert_eq!(message.participant, deserialized.participant);
    }

    #[cfg(feature = "debug")]
    #[test]
    fn test_debug_proto_message() {
        let message = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(false),
            id: Some("debug_test".to_string()),
            participant: None,
        };

        let debug_output = debug_proto_message(&message);
        assert!(debug_output.contains("<EMAIL>"));
        assert!(debug_output.contains("debug_test"));
    }
}
