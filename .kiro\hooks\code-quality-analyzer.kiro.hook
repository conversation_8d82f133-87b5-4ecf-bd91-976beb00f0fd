{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis including suggestions for improvements, code smells detection, design pattern recommendations, and best practices guidance", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.go", "**/*.rs", "**/*.py", "**/*.js", "**/*.ts", "**/*.java", "**/*.cpp", "**/*.c", "**/*.h"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Recommend language-specific best practices and coding standards\n4. **Readability**: Suggest improvements for code clarity and documentation\n5. **Maintainability**: Identify areas that could be refactored for better maintainability\n6. **Performance**: Point out potential performance optimizations without changing functionality\n\nProvide specific, actionable suggestions with code examples where helpful. Maintain a constructive tone and explain the reasoning behind each recommendation."}}