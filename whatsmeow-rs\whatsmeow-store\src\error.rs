//! Error types for storage operations

use thiserror::Error;

/// Errors that can occur during storage operations
#[derive(Debug, Error)]
pub enum StoreError {
    #[error("Database error: {message}")]
    Database { message: String },

    #[error("Serialization error: {message}")]
    Serialization { message: String },

    #[error("Not found: {resource}")]
    NotFound { resource: String },

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Invalid data: {reason}")]
    InvalidData { reason: String },

    #[error("Concurrent access error: {message}")]
    ConcurrentAccess { message: String },

    #[error("Storage full: {message}")]
    StorageFull { message: String },

    #[error("Corruption detected: {details}")]
    Corruption { details: String },

    #[error("Version mismatch: expected {expected}, found {found}")]
    VersionMismatch { expected: String, found: String },

    #[error("Permission denied: {operation}")]
    PermissionDenied { operation: String },

    #[error("Timeout: {operation} timed out after {duration_ms}ms")]
    Timeout { operation: String, duration_ms: u64 },

    #[error("Configuration error: {message}")]
    Configuration { message: String },

    #[error("Migration error: {message}")]
    Migration { message: String },

    #[error("Backup error: {message}")]
    Backup { message: String },

    #[error("Restore error: {message}")]
    Restore { message: String },
}

impl StoreError {
    /// Create a database error
    pub fn database<S: Into<String>>(message: S) -> Self {
        Self::Database {
            message: message.into(),
        }
    }

    /// Create a serialization error
    pub fn serialization<S: Into<String>>(message: S) -> Self {
        Self::Serialization {
            message: message.into(),
        }
    }

    /// Create a not found error
    pub fn not_found<S: Into<String>>(resource: S) -> Self {
        Self::NotFound {
            resource: resource.into(),
        }
    }

    /// Create an invalid data error
    pub fn invalid_data<S: Into<String>>(reason: S) -> Self {
        Self::InvalidData {
            reason: reason.into(),
        }
    }

    /// Create a concurrent access error
    pub fn concurrent_access<S: Into<String>>(message: S) -> Self {
        Self::ConcurrentAccess {
            message: message.into(),
        }
    }

    /// Create a storage full error
    pub fn storage_full<S: Into<String>>(message: S) -> Self {
        Self::StorageFull {
            message: message.into(),
        }
    }

    /// Create a corruption error
    pub fn corruption<S: Into<String>>(details: S) -> Self {
        Self::Corruption {
            details: details.into(),
        }
    }

    /// Create a version mismatch error
    pub fn version_mismatch<S: Into<String>>(expected: S, found: S) -> Self {
        Self::VersionMismatch {
            expected: expected.into(),
            found: found.into(),
        }
    }

    /// Create a permission denied error
    pub fn permission_denied<S: Into<String>>(operation: S) -> Self {
        Self::PermissionDenied {
            operation: operation.into(),
        }
    }

    /// Create a timeout error
    pub fn timeout<S: Into<String>>(operation: S, duration_ms: u64) -> Self {
        Self::Timeout {
            operation: operation.into(),
            duration_ms,
        }
    }

    /// Create a configuration error
    pub fn configuration<S: Into<String>>(message: S) -> Self {
        Self::Configuration {
            message: message.into(),
        }
    }

    /// Create a migration error
    pub fn migration<S: Into<String>>(message: S) -> Self {
        Self::Migration {
            message: message.into(),
        }
    }

    /// Create a backup error
    pub fn backup<S: Into<String>>(message: S) -> Self {
        Self::Backup {
            message: message.into(),
        }
    }

    /// Create a restore error
    pub fn restore<S: Into<String>>(message: S) -> Self {
        Self::Restore {
            message: message.into(),
        }
    }

    /// Check if this error is recoverable
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            Self::Timeout { .. } | Self::ConcurrentAccess { .. } | Self::Io(_)
        )
    }

    /// Check if this error indicates data corruption
    pub fn is_corruption(&self) -> bool {
        matches!(self, Self::Corruption { .. } | Self::InvalidData { .. })
    }
}

/// Result type alias for storage operations
pub type StoreResult<T> = Result<T, StoreError>;
