[{"name": "minimal_node_empty_content", "description": "Minimal node with empty string content", "input_bytes": "APgCE/8A", "input_hex": "00f80213ff00", "expected_node": {"tag": "message", "attributes": {}, "content": {"type": "binary", "value": ""}}}, {"name": "node_with_text_content", "description": "Node with UTF-8 text content", "input_bytes": "APgCE/wLaGVsbG8gd29ybGQ=", "input_hex": "00f80213fc0b68656c6c6f20776f726c64", "expected_node": {"tag": "message", "attributes": {}, "content": {"type": "text", "value": "hello world"}}}, {"name": "node_with_binary_content", "description": "Node with non-UTF8 binary content", "input_bytes": "APgCE/wE/wCA/g==", "input_hex": "00f80213fc04ff0080fe", "expected_node": {"tag": "message", "attributes": {}, "content": {"type": "binary", "value": "/wCA/g=="}}}, {"name": "node_with_attributes", "description": "Node with multiple attributes", "input_bytes": "APgGEwj8B3Rlc3QxMjME/ARjaGF0/wA=", "input_hex": "00f8061308fc077465737431323304fc0463686174ff00", "expected_node": {"tag": "message", "attributes": {"id": "test123", "type": "chat"}, "content": {"type": "binary", "value": ""}}}, {"name": "empty_string_serialization", "description": "Empty string content serialization - critical for compatibility", "input_bytes": "APgC/AR0ZXN0/wA=", "input_hex": "00f802fc0474657374ff00", "expected_node": {"tag": "test", "attributes": {}, "content": {"type": "binary", "value": ""}}}, {"name": "empty_binary_serialization", "description": "Empty binary content serialization", "input_bytes": "APgC/AR0ZXN0/AA=", "input_hex": "00f802fc0474657374fc00", "expected_node": {"tag": "test", "attributes": {}, "content": {"type": "binary", "value": ""}}}, {"name": "binary_with_protocol_tokens", "description": "Binary content containing protocol token bytes - must preserve as binary", "input_bytes": "APgC/ARkYXRh/Ab4+fz9/v8=", "input_hex": "00f802fc0464617461fc06f8f9fcfdfeff", "expected_node": {"tag": "data", "attributes": {}, "content": {"type": "binary", "value": "+Pn8/f7/"}}}, {"name": "truncated_binary_8", "description": "BINARY_8 token without size byte", "input_bytes": "/A==", "input_hex": "fc", "expected_error": {"type": "UnexpectedEndOfData", "message": "unexpected end of data"}}, {"name": "invalid_single_token", "description": "Invalid single-byte token", "input_bytes": "+ALI", "input_hex": "f802c8", "expected_error": {"type": "InvalidToken", "message": "invalid token", "code": 200}}, {"name": "list_size_too_small", "description": "List size too small for valid node", "input_bytes": "+AET", "input_hex": "f80113", "expected_error": {"type": "InvalidNodeFormat", "message": "node must have attributes or content"}}, {"name": "jid_pair_content", "description": "JID pair as node content", "input_bytes": "APgCE/oOAw==", "input_hex": "00f80213fa0e03", "expected_node": {"tag": "message", "attributes": {}, "content": {"type": "text", "value": "<EMAIL>"}}}, {"name": "nibble_packed_odd_length", "description": "Nibble-packed string with odd length", "input_bytes": "+AIT/4ES", "input_hex": "f80213ff8112", "expected_node": {"tag": "message", "attributes": {}, "content": {"type": "text", "value": "12"}}}, {"name": "list_8_boundary", "description": "Node at LIST_8 boundary (255 items)", "input_bytes": "APgB/AR0ZXN0", "input_hex": "00f801fc0474657374", "expected_node": {"tag": "test", "attributes": {"key0": "", "key1": "", "key10": "", "key100": "", "key101": "", "key102": "", "key103": "", "key104": "", "key105": "", "key106": "", "key107": "", "key108": "", "key109": "", "key11": "", "key110": "", "key111": "", "key112": "", "key113": "", "key114": "", "key115": "", "key116": "", "key117": "", "key118": "", "key119": "", "key12": "", "key120": "", "key121": "", "key122": "", "key123": "", "key124": "", "key125": "", "key126": "", "key13": "", "key14": "", "key15": "", "key16": "", "key17": "", "key18": "", "key19": "", "key2": "", "key20": "", "key21": "", "key22": "", "key23": "", "key24": "", "key25": "", "key26": "", "key27": "", "key28": "", "key29": "", "key3": "", "key30": "", "key31": "", "key32": "", "key33": "", "key34": "", "key35": "", "key36": "", "key37": "", "key38": "", "key39": "", "key4": "", "key40": "", "key41": "", "key42": "", "key43": "", "key44": "", "key45": "", "key46": "", "key47": "", "key48": "", "key49": "", "key5": "", "key50": "", "key51": "", "key52": "", "key53": "", "key54": "", "key55": "", "key56": "", "key57": "", "key58": "", "key59": "", "key6": "", "key60": "", "key61": "", "key62": "", "key63": "", "key64": "", "key65": "", "key66": "", "key67": "", "key68": "", "key69": "", "key7": "", "key70": "", "key71": "", "key72": "", "key73": "", "key74": "", "key75": "", "key76": "", "key77": "", "key78": "", "key79": "", "key8": "", "key80": "", "key81": "", "key82": "", "key83": "", "key84": "", "key85": "", "key86": "", "key87": "", "key88": "", "key89": "", "key9": "", "key90": "", "key91": "", "key92": "", "key93": "", "key94": "", "key95": "", "key96": "", "key97": "", "key98": "", "key99": ""}, "content": {"type": "none"}}}]