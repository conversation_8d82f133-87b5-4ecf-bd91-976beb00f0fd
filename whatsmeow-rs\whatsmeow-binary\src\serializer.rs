//! Binary node serializer

use crate::error::BinaryError;
use crate::node::{AttributeValue, Node, NodeContent};
use crate::token;

/// Binary node serializer
pub struct Serializer {
    buffer: Vec<u8>,
}

impl Serializer {
    /// Create a new serializer
    pub fn new() -> Self {
        Self { buffer: Vec::new() }
    }

    /// Serialize a node to bytes
    pub fn serialize_node(&mut self, node: &Node) -> Result<(), BinaryError> {
        // Validate tag is not empty (except for special "0" tag)
        if node.tag.is_empty() {
            return Err(BinaryError::SerializationFailed(
                "Empty tag not allowed".to_string(),
            ));
        }

        // Handle special case for "0" tag - serialize as minimal node
        if node.tag == "0" {
            self.write_byte(token::LIST_8);
            self.write_byte(1); // list size = 1 (just the tag)
            if let Some(token_index) = token::find_single_token("0") {
                self.write_byte(token_index);
            } else {
                // Fallback to raw string if token not found
                self.write_raw_string("0")?;
            }
            return Ok(());
        }

        // Calculate list size - following Go implementation exactly
        let has_content = match node.content {
            NodeContent::None => 0,
            _ => 1,
        };

        let attr_count = self.count_attributes(&node.attributes);
        let list_size = 2 * attr_count + 1 + has_content; // tag + (key,value pairs) + content

        // Write list start
        self.write_list_start(list_size)?;

        // Write tag (description)
        self.write_string(&node.tag)?;

        // Write attributes
        self.write_attributes(&node.attributes)?;

        // Write content if present
        if has_content > 0 {
            match &node.content {
                NodeContent::Text(text) => {
                    self.write_value(text)?;
                }
                NodeContent::Binary(data) => {
                    self.write_bytes_with_length(data)?;
                }
                NodeContent::Children(children) => {
                    // Always write the list start, even for empty lists
                    // This distinguishes empty children from no content
                    self.write_list_start(children.len())?;
                    for child in children {
                        self.serialize_node(child)?;
                    }
                }
                NodeContent::None => {} // Should not happen due to has_content check
            }
        }

        Ok(())
    }

    /// Get the serialized bytes
    pub fn into_bytes(self) -> Vec<u8> {
        self.buffer
    }

    /// Write a byte to the buffer
    fn write_byte(&mut self, byte: u8) {
        self.buffer.push(byte);
    }

    /// Write multiple bytes to the buffer
    fn write_bytes(&mut self, bytes: &[u8]) {
        self.buffer.extend_from_slice(bytes);
    }

    /// Write integer with specified byte count
    fn write_int(&mut self, value: u32, n: usize, little_endian: bool) {
        for i in 0..n {
            let shift = if little_endian { i } else { n - i - 1 } * 8;
            self.write_byte(((value >> shift) & 0xFF) as u8);
        }
    }

    /// Write 20-bit integer
    fn write_int20(&mut self, value: u32) {
        self.write_byte(((value >> 16) & 0x0F) as u8);
        self.write_byte(((value >> 8) & 0xFF) as u8);
        self.write_byte((value & 0xFF) as u8);
    }

    /// Write list start with size
    fn write_list_start(&mut self, size: usize) -> Result<(), BinaryError> {
        if size == 0 {
            self.write_byte(token::LIST_EMPTY);
        } else if size < 256 {
            self.write_byte(token::LIST_8);
            self.write_int(size as u32, 1, false);
        } else if size < 65536 {
            self.write_byte(token::LIST_16);
            self.write_int(size as u32, 2, false);
        } else {
            return Err(BinaryError::SerializationFailed(format!(
                "List size too large: {}",
                size
            )));
        }
        Ok(())
    }

    /// Write string value
    fn write_string(&mut self, value: &str) -> Result<(), BinaryError> {
        // Try single byte token first
        if let Some(token_index) = token::find_single_token(value) {
            self.write_byte(token_index);
            return Ok(());
        }

        // Try double byte token
        if let Some((dict_index, token_index)) = token::find_double_token(value) {
            self.write_byte(token::DICTIONARY_0 + dict_index);
            self.write_byte(token_index);
            return Ok(());
        }

        // Try packed formats
        if token::validate_nibble(value) {
            self.write_packed_string(value, token::NIBBLE_8)?;
            return Ok(());
        }

        if token::validate_hex(value) {
            self.write_packed_string(value, token::HEX_8)?;
            return Ok(());
        }

        // Fall back to raw string
        self.write_raw_string(value)
    }

    /// Write raw string with length prefix
    fn write_raw_string(&mut self, value: &str) -> Result<(), BinaryError> {
        let bytes = value.as_bytes();
        self.write_byte_length(bytes.len())?;
        self.write_bytes(bytes);
        Ok(())
    }

    /// Write packed string (nibble or hex)
    fn write_packed_string(&mut self, value: &str, data_type: u8) -> Result<(), BinaryError> {
        if value.len() > token::PACKED_MAX {
            return Err(BinaryError::SerializationFailed(format!(
                "String too long for packing: {}",
                value.len()
            )));
        }

        self.write_byte(data_type);

        let rounded_length = value.len().div_ceil(2);
        let mut length_byte = rounded_length as u8;
        if value.len() % 2 != 0 {
            length_byte |= 0x80; // Set high bit for odd length
        }
        self.write_byte(length_byte);

        let chars: Vec<char> = value.chars().collect();
        for i in (0..chars.len()).step_by(2) {
            let first = chars[i];
            let second = chars.get(i + 1).copied().unwrap_or('\0');

            let first_packed = match data_type {
                token::NIBBLE_8 => token::pack_nibble(first),
                token::HEX_8 => token::pack_hex(first),
                _ => {
                    return Err(BinaryError::SerializationFailed(
                        "Invalid pack type".to_string(),
                    ))
                }
            };

            let second_packed = match data_type {
                token::NIBBLE_8 => token::pack_nibble(second),
                token::HEX_8 => token::pack_hex(second),
                _ => {
                    return Err(BinaryError::SerializationFailed(
                        "Invalid pack type".to_string(),
                    ))
                }
            };

            match (first_packed, second_packed) {
                (Some(f), Some(s)) => {
                    self.write_byte((f << 4) | s);
                }
                _ => {
                    return Err(BinaryError::SerializationFailed(
                        "Invalid character for packing".to_string(),
                    ))
                }
            }
        }

        Ok(())
    }

    /// Write byte length prefix
    fn write_byte_length(&mut self, length: usize) -> Result<(), BinaryError> {
        if length < 256 {
            self.write_byte(token::BINARY_8);
            self.write_int(length as u32, 1, false);
        } else if length < (1 << 20) {
            self.write_byte(token::BINARY_20);
            self.write_int20(length as u32);
        } else if length < (1u32 << 31) as usize {
            self.write_byte(token::BINARY_32);
            self.write_int(length as u32, 4, false);
        } else {
            return Err(BinaryError::SerializationFailed(format!(
                "Length too large: {}",
                length
            )));
        }
        Ok(())
    }

    /// Write bytes with length prefix
    fn write_bytes_with_length(&mut self, data: &[u8]) -> Result<(), BinaryError> {
        self.write_byte_length(data.len())?;
        self.write_bytes(data);
        Ok(())
    }

    /// Write attribute value
    fn write_attribute_value(&mut self, value: &AttributeValue) -> Result<(), BinaryError> {
        match value {
            AttributeValue::String(s) => self.write_string(s),
            AttributeValue::Integer(i) => self.write_string(&i.to_string()),
            AttributeValue::Boolean(b) => self.write_string(&b.to_string()),
            AttributeValue::Binary(data) => {
                // Binary data in attributes should be written as binary with length
                self.write_bytes_with_length(data)
            }
            AttributeValue::JID(jid) => {
                // For now, write JIDs as JID pairs
                if let Some(at_pos) = jid.find('@') {
                    let (user, server) = jid.split_at(at_pos);
                    let server = &server[1..]; // Remove '@'

                    self.write_byte(token::JID_PAIR);
                    if user.is_empty() {
                        self.write_byte(token::LIST_EMPTY);
                    } else {
                        self.write_string(user)?;
                    }
                    self.write_string(server)?;
                } else {
                    // No @ symbol, treat as server only
                    self.write_byte(token::JID_PAIR);
                    self.write_byte(token::LIST_EMPTY);
                    self.write_string(jid)?;
                }
                Ok(())
            }
        }
    }

    /// Write attributes
    fn write_attributes(
        &mut self,
        attributes: &std::collections::HashMap<String, AttributeValue>,
    ) -> Result<(), BinaryError> {
        for (key, value) in attributes {
            self.write_string(key)?;
            self.write_attribute_value(value)?;
        }
        Ok(())
    }

    /// Write a value (similar to Go's write method)
    fn write_value(&mut self, value: &str) -> Result<(), BinaryError> {
        // Special handling for empty strings - they should be written as BINARY_8 with length 0
        // not as LIST_EMPTY, because LIST_EMPTY means no content, not empty string content
        if value.is_empty() {
            self.write_byte(token::BINARY_8);
            self.write_byte(0); // length 0
            return Ok(());
        }
        self.write_string(value)
    }

    /// Count non-empty attributes
    fn count_attributes(
        &self,
        attributes: &std::collections::HashMap<String, AttributeValue>,
    ) -> usize {
        attributes
            .iter()
            .filter(|(_, value)| match value {
                AttributeValue::String(s) => !s.is_empty(),
                _ => true,
            })
            .count()
    }
}

impl Default for Serializer {
    fn default() -> Self {
        Self::new()
    }
}
