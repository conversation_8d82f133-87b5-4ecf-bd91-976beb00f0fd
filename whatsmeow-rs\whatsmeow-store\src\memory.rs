//! In-memory storage implementation for testing

use crate::error::StoreError;
use crate::traits::{
    DeviceStore, IdentityStore, PreKey, PreKeyStore, SenderKeyStore, SessionStore, StorageStats,
    Store,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use whatsmeow_types::Device;

/// In-memory storage implementation
#[derive(Debu<PERSON>, Default)]
pub struct MemoryStore {
    device: Arc<RwLock<Option<Device>>>,
    sessions: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    prekeys: Arc<RwLock<HashMap<u32, PreKey>>>,
    sender_keys: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    identity_keys: Arc<RwLock<HashMap<String, Vec<u8>>>>,
}

impl MemoryStore {
    pub fn new() -> Self {
        Self::default()
    }

    /// Helper method to get the next available prekey ID
    fn get_next_prekey_id_internal(&self, prekeys: &HashMap<u32, PreKey>) -> u32 {
        prekeys.keys().max().map(|id| id + 1).unwrap_or(1)
    }
}

#[async_trait]
impl DeviceStore for MemoryStore {
    async fn get_device(&self) -> Result<Option<Device>, StoreError> {
        let device = self.device.read().await;
        Ok(device.clone())
    }

    async fn put_device(&self, device: &Device) -> Result<(), StoreError> {
        let mut store_device = self.device.write().await;
        *store_device = Some(device.clone());
        Ok(())
    }

    async fn delete_device(&self) -> Result<(), StoreError> {
        let mut device = self.device.write().await;
        *device = None;
        Ok(())
    }
}

#[async_trait]
impl SessionStore for MemoryStore {
    async fn get_session(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError> {
        let sessions = self.sessions.read().await;
        Ok(sessions.get(address).cloned())
    }

    async fn put_session(&self, address: &str, session: &[u8]) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.insert(address.to_string(), session.to_vec());
        Ok(())
    }

    async fn delete_session(&self, address: &str) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.remove(address);
        Ok(())
    }

    async fn delete_all_sessions(&self, phone: &str) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.retain(|key, _| !key.starts_with(phone));
        Ok(())
    }

    async fn get_sessions_for_phone(
        &self,
        phone: &str,
    ) -> Result<HashMap<String, Vec<u8>>, StoreError> {
        let sessions = self.sessions.read().await;
        let filtered: HashMap<String, Vec<u8>> = sessions
            .iter()
            .filter(|(key, _)| key.starts_with(phone))
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect();
        Ok(filtered)
    }

    async fn get_session_count(&self) -> Result<usize, StoreError> {
        let sessions = self.sessions.read().await;
        Ok(sessions.len())
    }

    async fn clear_all_sessions(&self) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.clear();
        Ok(())
    }
}

#[async_trait]
impl PreKeyStore for MemoryStore {
    async fn get_or_gen_prekeys(&self, count: u32) -> Result<Vec<PreKey>, StoreError> {
        let mut prekeys = self.prekeys.write().await;
        let mut result = Vec::new();

        // Generate new prekeys if needed
        let start_id = self.get_next_prekey_id_internal(&prekeys);
        for i in 0..count {
            let id = start_id + i;
            let prekey = PreKey::new(id, vec![0; 32]); // Placeholder key data
            prekeys.insert(id, prekey.clone());
            result.push(prekey);
        }

        Ok(result)
    }

    async fn get_prekey(&self, id: u32) -> Result<Option<PreKey>, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(prekeys.get(&id).cloned())
    }

    async fn remove_prekey(&self, id: u32) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        prekeys.remove(&id);
        Ok(())
    }

    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        for (id, prekey) in prekeys.iter_mut() {
            if *id <= up_to_id {
                prekey.mark_uploaded();
            }
        }
        Ok(())
    }

    async fn get_all_prekeys(&self) -> Result<Vec<PreKey>, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(prekeys.values().cloned().collect())
    }

    async fn get_unuploaded_prekeys(&self) -> Result<Vec<PreKey>, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(prekeys
            .values()
            .filter(|prekey| !prekey.is_uploaded())
            .cloned()
            .collect())
    }

    async fn get_prekey_count(&self) -> Result<usize, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(prekeys.len())
    }

    async fn remove_prekeys(&self, ids: &[u32]) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        for id in ids {
            prekeys.remove(id);
        }
        Ok(())
    }

    async fn clear_all_prekeys(&self) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        prekeys.clear();
        Ok(())
    }

    async fn get_next_prekey_id(&self) -> Result<u32, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(self.get_next_prekey_id_internal(&prekeys))
    }
}

#[async_trait]
impl SenderKeyStore for MemoryStore {
    async fn get_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
    ) -> Result<Option<Vec<u8>>, StoreError> {
        let key = format!("{}:{}", group_id, sender_id);
        let sender_keys = self.sender_keys.read().await;
        Ok(sender_keys.get(&key).cloned())
    }

    async fn put_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
        key: &[u8],
    ) -> Result<(), StoreError> {
        let key_id = format!("{}:{}", group_id, sender_id);
        let mut sender_keys = self.sender_keys.write().await;
        sender_keys.insert(key_id, key.to_vec());
        Ok(())
    }

    async fn delete_sender_key(&self, group_id: &str, sender_id: &str) -> Result<(), StoreError> {
        let key = format!("{}:{}", group_id, sender_id);
        let mut sender_keys = self.sender_keys.write().await;
        sender_keys.remove(&key);
        Ok(())
    }

    async fn get_group_sender_keys(
        &self,
        group_id: &str,
    ) -> Result<HashMap<String, Vec<u8>>, StoreError> {
        let sender_keys = self.sender_keys.read().await;
        let prefix = format!("{}:", group_id);
        let filtered: HashMap<String, Vec<u8>> = sender_keys
            .iter()
            .filter(|(key, _)| key.starts_with(&prefix))
            .map(|(k, v)| {
                // Extract sender_id from "group_id:sender_id" format
                let sender_id = k.strip_prefix(&prefix).unwrap_or(k);
                (sender_id.to_string(), v.clone())
            })
            .collect();
        Ok(filtered)
    }

    async fn delete_group_sender_keys(&self, group_id: &str) -> Result<(), StoreError> {
        let mut sender_keys = self.sender_keys.write().await;
        let prefix = format!("{}:", group_id);
        sender_keys.retain(|key, _| !key.starts_with(&prefix));
        Ok(())
    }

    async fn clear_all_sender_keys(&self) -> Result<(), StoreError> {
        let mut sender_keys = self.sender_keys.write().await;
        sender_keys.clear();
        Ok(())
    }
}

#[async_trait]
impl IdentityStore for MemoryStore {
    async fn get_identity_key(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError> {
        let identity_keys = self.identity_keys.read().await;
        Ok(identity_keys.get(address).cloned())
    }

    async fn put_identity_key(&self, address: &str, key: &[u8]) -> Result<(), StoreError> {
        let mut identity_keys = self.identity_keys.write().await;
        identity_keys.insert(address.to_string(), key.to_vec());
        Ok(())
    }

    async fn is_trusted_identity(&self, address: &str, key: &[u8]) -> Result<bool, StoreError> {
        let identity_keys = self.identity_keys.read().await;
        match identity_keys.get(address) {
            Some(stored_key) => Ok(stored_key == key),
            None => Ok(true), // Trust on first use
        }
    }

    async fn delete_identity_key(&self, address: &str) -> Result<(), StoreError> {
        let mut identity_keys = self.identity_keys.write().await;
        identity_keys.remove(address);
        Ok(())
    }

    async fn get_all_identity_keys(&self) -> Result<HashMap<String, Vec<u8>>, StoreError> {
        let identity_keys = self.identity_keys.read().await;
        Ok(identity_keys.clone())
    }

    async fn mark_identity_trusted(&self, address: &str, key: &[u8]) -> Result<(), StoreError> {
        // In memory store, we just store the key as trusted
        self.put_identity_key(address, key).await
    }

    async fn mark_identity_untrusted(&self, address: &str) -> Result<(), StoreError> {
        // In memory store, we remove untrusted keys
        self.delete_identity_key(address).await
    }

    async fn clear_all_identity_keys(&self) -> Result<(), StoreError> {
        let mut identity_keys = self.identity_keys.write().await;
        identity_keys.clear();
        Ok(())
    }
}

#[async_trait]
impl Store for MemoryStore {
    async fn get_stats(&self) -> Result<StorageStats, StoreError> {
        let device_count = if self.device.read().await.is_some() {
            1
        } else {
            0
        };
        let session_count = self.sessions.read().await.len();
        let prekey_count = self.prekeys.read().await.len();
        let sender_key_count = self.sender_keys.read().await.len();
        let identity_key_count = self.identity_keys.read().await.len();

        // Calculate approximate size
        let sessions = self.sessions.read().await;
        let prekeys = self.prekeys.read().await;
        let sender_keys = self.sender_keys.read().await;
        let identity_keys = self.identity_keys.read().await;

        let total_size_bytes = sessions.values().map(|v| v.len()).sum::<usize>()
            + prekeys.values().map(|p| p.key_pair.len()).sum::<usize>()
            + sender_keys.values().map(|v| v.len()).sum::<usize>()
            + identity_keys.values().map(|v| v.len()).sum::<usize>();

        Ok(StorageStats {
            device_count,
            session_count,
            prekey_count,
            sender_key_count,
            identity_key_count,
            total_size_bytes: total_size_bytes as u64,
        })
    }

    async fn clear_all(&self) -> Result<(), StoreError> {
        self.delete_device().await?;
        self.clear_all_sessions().await?;
        self.clear_all_prekeys().await?;
        self.clear_all_sender_keys().await?;
        self.clear_all_identity_keys().await?;
        Ok(())
    }

    async fn backup(&self) -> Result<Vec<u8>, StoreError> {
        // Simple JSON serialization for backup
        let device = self.device.read().await.clone();
        let sessions = self.sessions.read().await.clone();
        let prekeys = self.prekeys.read().await.clone();
        let sender_keys = self.sender_keys.read().await.clone();
        let identity_keys = self.identity_keys.read().await.clone();

        let backup_data = serde_json::json!({
            "version": "1.0",
            "device": device,
            "sessions": sessions,
            "prekeys": prekeys,
            "sender_keys": sender_keys,
            "identity_keys": identity_keys,
            "timestamp": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        });

        serde_json::to_vec(&backup_data)
            .map_err(|e| StoreError::serialization(format!("Failed to serialize backup: {}", e)))
    }

    async fn restore(&self, data: &[u8]) -> Result<(), StoreError> {
        let backup_data: serde_json::Value = serde_json::from_slice(data).map_err(|e| {
            StoreError::serialization(format!("Failed to deserialize backup: {}", e))
        })?;

        // Check version compatibility
        let version = backup_data
            .get("version")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        if version != "1.0" {
            return Err(StoreError::version_mismatch("1.0", version));
        }

        // Clear existing data
        self.clear_all().await?;

        // Restore device
        if let Some(device_data) = backup_data.get("device") {
            if !device_data.is_null() {
                let device: Device = serde_json::from_value(device_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize device: {}", e))
                })?;
                self.put_device(&device).await?;
            }
        }

        // Restore sessions
        if let Some(sessions_data) = backup_data.get("sessions") {
            let sessions: HashMap<String, Vec<u8>> = serde_json::from_value(sessions_data.clone())
                .map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize sessions: {}", e))
                })?;
            for (address, session) in sessions {
                self.put_session(&address, &session).await?;
            }
        }

        // Restore prekeys
        if let Some(prekeys_data) = backup_data.get("prekeys") {
            let prekeys: HashMap<u32, PreKey> = serde_json::from_value(prekeys_data.clone())
                .map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize prekeys: {}", e))
                })?;
            let mut prekeys_store = self.prekeys.write().await;
            *prekeys_store = prekeys;
        }

        // Restore sender keys
        if let Some(sender_keys_data) = backup_data.get("sender_keys") {
            let sender_keys: HashMap<String, Vec<u8>> =
                serde_json::from_value(sender_keys_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize sender keys: {}", e))
                })?;
            for (key, value) in sender_keys {
                let parts: Vec<&str> = key.splitn(2, ':').collect();
                if parts.len() == 2 {
                    self.put_sender_key(parts[0], parts[1], &value).await?;
                }
            }
        }

        // Restore identity keys
        if let Some(identity_keys_data) = backup_data.get("identity_keys") {
            let identity_keys: HashMap<String, Vec<u8>> =
                serde_json::from_value(identity_keys_data.clone()).map_err(|e| {
                    StoreError::serialization(format!("Failed to deserialize identity keys: {}", e))
                })?;
            for (address, key) in identity_keys {
                self.put_identity_key(&address, &key).await?;
            }
        }

        Ok(())
    }

    async fn validate(&self) -> Result<Vec<String>, StoreError> {
        let mut issues = Vec::new();

        // Validate device
        if let Some(device) = self.device.read().await.as_ref() {
            if device.jid.user.is_empty() {
                issues.push("Device JID user is empty".to_string());
            }
            if device.jid.server.is_empty() {
                issues.push("Device JID server is empty".to_string());
            }
        }

        // Validate sessions
        let sessions = self.sessions.read().await;
        for (address, session_data) in sessions.iter() {
            if address.is_empty() {
                issues.push("Empty session address found".to_string());
            }
            if session_data.is_empty() {
                issues.push(format!("Empty session data for address: {}", address));
            }
        }

        // Validate prekeys
        let prekeys = self.prekeys.read().await;
        for (id, prekey) in prekeys.iter() {
            if prekey.id != *id {
                issues.push(format!(
                    "PreKey ID mismatch: stored as {} but has ID {}",
                    id, prekey.id
                ));
            }
            if prekey.key_pair.is_empty() {
                issues.push(format!("Empty key pair for PreKey {}", id));
            }
        }

        // Validate sender keys
        let sender_keys = self.sender_keys.read().await;
        for (key, data) in sender_keys.iter() {
            if !key.contains(':') {
                issues.push(format!("Invalid sender key format: {}", key));
            }
            if data.is_empty() {
                issues.push(format!("Empty sender key data for: {}", key));
            }
        }

        // Validate identity keys
        let identity_keys = self.identity_keys.read().await;
        for (address, key_data) in identity_keys.iter() {
            if address.is_empty() {
                issues.push("Empty identity key address found".to_string());
            }
            if key_data.is_empty() {
                issues.push(format!("Empty identity key data for address: {}", address));
            }
        }

        Ok(issues)
    }
}
#[cfg(test)]
mod tests {
    use super::*;
    use whatsmeow_types::{device::DevicePlatform, Device, Jid};

    fn create_test_device() -> Device {
        let jid = Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 0);
        Device::new(jid, 12345, DevicePlatform::Web).unwrap()
    }

    #[tokio::test]
    async fn test_device_store() {
        let store = MemoryStore::new();

        // Initially no device
        assert!(store.get_device().await.unwrap().is_none());
        assert!(!store.has_device().await.unwrap());
        assert!(store.get_device_jid().await.unwrap().is_none());

        // Store a device
        let device = create_test_device();
        store.put_device(&device).await.unwrap();

        // Retrieve device
        let retrieved = store.get_device().await.unwrap().unwrap();
        assert_eq!(retrieved.jid, device.jid);
        assert_eq!(retrieved.registration_id, device.registration_id);
        assert!(store.has_device().await.unwrap());
        assert_eq!(store.get_device_jid().await.unwrap().unwrap(), device.jid);

        // Delete device
        store.delete_device().await.unwrap();
        assert!(store.get_device().await.unwrap().is_none());
        assert!(!store.has_device().await.unwrap());
    }

    #[tokio::test]
    async fn test_session_store() {
        let store = MemoryStore::new();
        let address = "<EMAIL>";
        let session_data = vec![1, 2, 3, 4, 5];

        // Initially no session
        assert!(store.get_session(address).await.unwrap().is_none());
        assert!(!store.has_session(address).await.unwrap());
        assert_eq!(store.get_session_count().await.unwrap(), 0);

        // Store session
        store.put_session(address, &session_data).await.unwrap();

        // Retrieve session
        let retrieved = store.get_session(address).await.unwrap().unwrap();
        assert_eq!(retrieved, session_data);
        assert!(store.has_session(address).await.unwrap());
        assert_eq!(store.get_session_count().await.unwrap(), 1);

        // Test phone-based operations
        let phone = "1234567890";
        let sessions = store.get_sessions_for_phone(phone).await.unwrap();
        assert_eq!(sessions.len(), 1);
        assert!(sessions.contains_key(address));

        // Delete all sessions for phone
        store.delete_all_sessions(phone).await.unwrap();
        assert!(store.get_session(address).await.unwrap().is_none());
        assert_eq!(store.get_session_count().await.unwrap(), 0);

        // Test clear all sessions
        store.put_session(address, &session_data).await.unwrap();
        store
            .put_session("<EMAIL>", &session_data)
            .await
            .unwrap();
        assert_eq!(store.get_session_count().await.unwrap(), 2);

        store.clear_all_sessions().await.unwrap();
        assert_eq!(store.get_session_count().await.unwrap(), 0);
    }

    #[tokio::test]
    async fn test_prekey_store() {
        let store = MemoryStore::new();

        // Initially no prekeys
        assert_eq!(store.get_prekey_count().await.unwrap(), 0);
        assert!(store.get_all_prekeys().await.unwrap().is_empty());
        assert!(store.get_unuploaded_prekeys().await.unwrap().is_empty());

        // Generate prekeys
        let prekeys = store.get_or_gen_prekeys(5).await.unwrap();
        assert_eq!(prekeys.len(), 5);
        assert_eq!(store.get_prekey_count().await.unwrap(), 5);

        // Check prekey IDs are sequential
        for (i, prekey) in prekeys.iter().enumerate() {
            assert_eq!(prekey.id, (i + 1) as u32);
            assert!(!prekey.is_uploaded());
        }

        // Get specific prekey
        let prekey = store.get_prekey(1).await.unwrap().unwrap();
        assert_eq!(prekey.id, 1);

        // Mark prekeys as uploaded
        store.mark_prekeys_as_uploaded(3).await.unwrap();
        let unuploaded = store.get_unuploaded_prekeys().await.unwrap();
        assert_eq!(unuploaded.len(), 2); // Only 4 and 5 should be unuploaded

        // Remove prekeys
        store.remove_prekeys(&[1, 2]).await.unwrap();
        assert_eq!(store.get_prekey_count().await.unwrap(), 3);
        assert!(store.get_prekey(1).await.unwrap().is_none());

        // Clear all prekeys
        store.clear_all_prekeys().await.unwrap();
        assert_eq!(store.get_prekey_count().await.unwrap(), 0);

        // Test next prekey ID
        let next_id = store.get_next_prekey_id().await.unwrap();
        assert_eq!(next_id, 1); // Should start from 1 when empty
    }

    #[tokio::test]
    async fn test_sender_key_store() {
        let store = MemoryStore::new();
        let group_id = "group123";
        let sender_id = "sender456";
        let key_data = vec![1, 2, 3, 4, 5];

        // Initially no sender keys
        assert!(store
            .get_sender_key(group_id, sender_id)
            .await
            .unwrap()
            .is_none());
        assert!(!store.has_sender_key(group_id, sender_id).await.unwrap());

        // Store sender key
        store
            .put_sender_key(group_id, sender_id, &key_data)
            .await
            .unwrap();

        // Retrieve sender key
        let retrieved = store
            .get_sender_key(group_id, sender_id)
            .await
            .unwrap()
            .unwrap();
        assert_eq!(retrieved, key_data);
        assert!(store.has_sender_key(group_id, sender_id).await.unwrap());

        // Test group operations
        let group_keys = store.get_group_sender_keys(group_id).await.unwrap();
        assert_eq!(group_keys.len(), 1);
        assert!(group_keys.contains_key(sender_id));

        // Add another sender key for the same group
        store
            .put_sender_key(group_id, "sender789", &key_data)
            .await
            .unwrap();
        let group_keys = store.get_group_sender_keys(group_id).await.unwrap();
        assert_eq!(group_keys.len(), 2);

        // Delete specific sender key
        store.delete_sender_key(group_id, sender_id).await.unwrap();
        assert!(store
            .get_sender_key(group_id, sender_id)
            .await
            .unwrap()
            .is_none());
        let group_keys = store.get_group_sender_keys(group_id).await.unwrap();
        assert_eq!(group_keys.len(), 1);

        // Delete all group sender keys
        store.delete_group_sender_keys(group_id).await.unwrap();
        let group_keys = store.get_group_sender_keys(group_id).await.unwrap();
        assert!(group_keys.is_empty());

        // Test clear all sender keys
        store
            .put_sender_key(group_id, sender_id, &key_data)
            .await
            .unwrap();
        store
            .put_sender_key("group456", sender_id, &key_data)
            .await
            .unwrap();
        store.clear_all_sender_keys().await.unwrap();
        assert!(store
            .get_sender_key(group_id, sender_id)
            .await
            .unwrap()
            .is_none());
        assert!(store
            .get_sender_key("group456", sender_id)
            .await
            .unwrap()
            .is_none());
    }

    #[tokio::test]
    async fn test_identity_store() {
        let store = MemoryStore::new();
        let address = "<EMAIL>";
        let key_data = vec![1, 2, 3, 4, 5];

        // Initially no identity keys
        assert!(store.get_identity_key(address).await.unwrap().is_none());
        assert!(!store.has_identity_key(address).await.unwrap());

        // Store identity key
        store.put_identity_key(address, &key_data).await.unwrap();

        // Retrieve identity key
        let retrieved = store.get_identity_key(address).await.unwrap().unwrap();
        assert_eq!(retrieved, key_data);
        assert!(store.has_identity_key(address).await.unwrap());

        // Test trust (should be trusted since it's the same key)
        assert!(store.is_trusted_identity(address, &key_data).await.unwrap());

        // Test with different key (should not be trusted)
        let different_key = vec![6, 7, 8, 9, 10];
        assert!(!store
            .is_trusted_identity(address, &different_key)
            .await
            .unwrap());

        // Test trust on first use (new address should be trusted)
        assert!(store
            .is_trusted_identity("<EMAIL>", &different_key)
            .await
            .unwrap());

        // Test get all identity keys
        let all_keys = store.get_all_identity_keys().await.unwrap();
        assert_eq!(all_keys.len(), 1);
        assert!(all_keys.contains_key(address));

        // Test mark as trusted/untrusted
        store
            .mark_identity_trusted(address, &different_key)
            .await
            .unwrap();
        let retrieved = store.get_identity_key(address).await.unwrap().unwrap();
        assert_eq!(retrieved, different_key);

        store.mark_identity_untrusted(address).await.unwrap();
        assert!(store.get_identity_key(address).await.unwrap().is_none());

        // Test clear all identity keys
        store.put_identity_key(address, &key_data).await.unwrap();
        store
            .put_identity_key("<EMAIL>", &key_data)
            .await
            .unwrap();
        let all_keys = store.get_all_identity_keys().await.unwrap();
        assert_eq!(all_keys.len(), 2);

        store.clear_all_identity_keys().await.unwrap();
        let all_keys = store.get_all_identity_keys().await.unwrap();
        assert!(all_keys.is_empty());
    }

    #[tokio::test]
    async fn test_store_trait() {
        let store = MemoryStore::new();
        let device = create_test_device();

        // Test stats on empty store
        let stats = store.get_stats().await.unwrap();
        assert!(stats.is_empty());
        assert_eq!(stats.device_count, 0);
        assert_eq!(stats.session_count, 0);
        assert_eq!(stats.prekey_count, 0);
        assert_eq!(stats.sender_key_count, 0);
        assert_eq!(stats.identity_key_count, 0);

        // Add some data
        store.put_device(&device).await.unwrap();
        store
            .put_session("<EMAIL>", &[1, 2, 3])
            .await
            .unwrap();
        store.get_or_gen_prekeys(2).await.unwrap();
        store
            .put_sender_key("group1", "sender1", &[4, 5, 6])
            .await
            .unwrap();
        store
            .put_identity_key("<EMAIL>", &[7, 8, 9])
            .await
            .unwrap();

        // Test stats with data
        let stats = store.get_stats().await.unwrap();
        assert!(!stats.is_empty());
        assert_eq!(stats.device_count, 1);
        assert_eq!(stats.session_count, 1);
        assert_eq!(stats.prekey_count, 2);
        assert_eq!(stats.sender_key_count, 1);
        assert_eq!(stats.identity_key_count, 1);
        assert!(stats.total_size_bytes > 0);

        // Test backup and restore
        let backup_data = store.backup().await.unwrap();
        assert!(!backup_data.is_empty());

        // Clear store and restore
        store.clear_all().await.unwrap();
        let stats = store.get_stats().await.unwrap();
        assert!(stats.is_empty());

        store.restore(&backup_data).await.unwrap();
        let stats = store.get_stats().await.unwrap();
        assert!(!stats.is_empty());
        assert_eq!(stats.device_count, 1);
        assert_eq!(stats.session_count, 1);
        assert_eq!(stats.prekey_count, 2);
        assert_eq!(stats.sender_key_count, 1);
        assert_eq!(stats.identity_key_count, 1);

        // Verify restored data
        let restored_device = store.get_device().await.unwrap().unwrap();
        assert_eq!(restored_device.jid, device.jid);
        assert!(store
            .get_session("<EMAIL>")
            .await
            .unwrap()
            .is_some());
        assert_eq!(store.get_prekey_count().await.unwrap(), 2);
        assert!(store
            .get_sender_key("group1", "sender1")
            .await
            .unwrap()
            .is_some());
        assert!(store
            .get_identity_key("<EMAIL>")
            .await
            .unwrap()
            .is_some());

        // Test validation
        let issues = store.validate().await.unwrap();
        assert!(issues.is_empty()); // Should have no validation issues
    }

    #[tokio::test]
    async fn test_validation_with_issues() {
        let store = MemoryStore::new();

        // Create device with empty JID user (invalid)
        let mut invalid_device = create_test_device();
        invalid_device.jid.user = String::new();
        store.put_device(&invalid_device).await.unwrap();

        // Add empty session data
        store.put_session("<EMAIL>", &[]).await.unwrap();

        // Add empty identity key
        store.put_identity_key("", &[]).await.unwrap();

        let issues = store.validate().await.unwrap();
        assert!(!issues.is_empty());

        // Should have multiple validation issues
        let issues_str = issues.join(", ");
        assert!(issues_str.contains("Device JID user is empty"));
        assert!(issues_str.contains("Empty session data"));
        assert!(issues_str.contains("Empty identity key"));
    }

    #[tokio::test]
    async fn test_backup_restore_error_handling() {
        let store = MemoryStore::new();

        // Test restore with invalid data
        let invalid_data = b"invalid json";
        let result = store.restore(invalid_data).await;
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            StoreError::Serialization { .. }
        ));

        // Test restore with wrong version
        let wrong_version = serde_json::json!({
            "version": "2.0",
            "device": null,
            "sessions": {},
            "prekeys": {},
            "sender_keys": {},
            "identity_keys": {}
        });
        let wrong_version_data = serde_json::to_vec(&wrong_version).unwrap();
        let result = store.restore(&wrong_version_data).await;
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            StoreError::VersionMismatch { .. }
        ));
    }

    #[tokio::test]
    async fn test_concurrent_access() {
        let store = Arc::new(MemoryStore::new());
        let mut handles = Vec::new();

        // Test concurrent session operations
        for i in 0..10 {
            let store_clone = Arc::clone(&store);
            let handle = tokio::spawn(async move {
                let address = format!("test{}@s.whatsapp.net", i);
                let session_data = vec![i as u8; 10];

                store_clone
                    .put_session(&address, &session_data)
                    .await
                    .unwrap();
                let retrieved = store_clone.get_session(&address).await.unwrap().unwrap();
                assert_eq!(retrieved, session_data);
            });
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }

        // Verify all sessions were stored
        assert_eq!(store.get_session_count().await.unwrap(), 10);
    }

    #[tokio::test]
    async fn test_prekey_generation_sequential_ids() {
        let store = MemoryStore::new();

        // Generate first batch
        let batch1 = store.get_or_gen_prekeys(3).await.unwrap();
        assert_eq!(batch1.len(), 3);
        assert_eq!(batch1[0].id, 1);
        assert_eq!(batch1[1].id, 2);
        assert_eq!(batch1[2].id, 3);

        // Generate second batch (should continue from where first batch left off)
        let batch2 = store.get_or_gen_prekeys(2).await.unwrap();
        assert_eq!(batch2.len(), 2);
        assert_eq!(batch2[0].id, 4);
        assert_eq!(batch2[1].id, 5);

        // Total should be 5 prekeys
        assert_eq!(store.get_prekey_count().await.unwrap(), 5);
    }
}
