//! Protocol buffer conversion utilities for WhatsApp message types.
//!
//! This module provides conversion traits and helper functions for converting
//! between protocol buffer types and internal Rust types. It handles:
//!
//! - Type-safe conversions with comprehensive error handling
//! - Timestamp conversions between different time representations
//! - Base64 encoding/decoding for binary data
//! - JID parsing and formatting
//! - Message type enumeration conversions
//!
//! # Key Traits
//!
//! - [`FromProto`]: Convert from protocol buffer types to Rust types
//! - [`ToProto`]: Convert from Rust types to protocol buffer types
//! - [`ProtoConvert`]: Bidirectional conversion trait
//!
//! # Recent Improvements
//!
//! - Refactored complex parsing functions into smaller, focused units
//! - Enhanced error handling with detailed validation messages
//! - Added Builder pattern for Account proto construction
//! - Optimized string allocations with constants for common values
//! - Improved phone number validation with comprehensive edge case handling
//! - Added comprehensive test coverage for edge cases and error conditions
//!
//! # Usage Guide
//!
//! ## Converting Protocol Buffer Messages
//!
//! ```rust
//! use whatsmeow_proto::conversion::{FromProto, ToProto};
//! use whatsmeow_types::{Jid, MessageId};
//!
//! // Convert from proto to Rust type
//! let account_proto = wa_proto::Account {
//!     username: Some("<EMAIL>".to_string()),
//!     // ... other fields
//! };
//! let jid: Jid = FromProto::from_proto(account_proto)?;
//!
//! // Convert from Rust type to proto using builder pattern
//! let proto = AccountBuilder::new()
//!     .with_jid(&jid)
//!     .with_country_code("US".to_string())
//!     .build();
//! ```
//!
//! ## Error Handling
//!
//! All conversion functions return `Result` types with detailed error information:
//!
//! ```rust
//! match FromProto::from_proto(proto_message) {
//!     Ok(rust_type) => {
//!         // Handle successful conversion
//!     },
//!     Err(ProtoError::MissingRequiredField(field)) => {
//!         eprintln!("Missing required field: {}", field);
//!     },
//!     Err(ProtoError::TypeConversionFailed(msg)) => {
//!         eprintln!("Type conversion failed: {}", msg);
//!     },
//!     // ... handle other error types
//! }
//! ```
//!
//! ## Timestamp Handling
//!
//! The module provides generic timestamp handling with compile-time unit safety:
//!
//! ```rust
//! use whatsmeow_proto::conversion::{SecondTimestamp, MillisTimestamp};
//! use std::time::SystemTime;
//!
//! // Create timestamps with specific units
//! let sec_timestamp = SecondTimestamp::new(1640995200);
//! let millis_timestamp = MillisTimestamp::new(1640995200000);
//!
//! // Convert to SystemTime
//! let system_time = sec_timestamp.to_system_time();
//! ```

use crate::error::ProtoError;
use crate::wa_proto;
use std::time::{SystemTime, UNIX_EPOCH};
use whatsmeow_types::message::MessageType;
use whatsmeow_types::{Jid, MessageId};

/// Helper trait for required field extraction with better error messages
///
/// This trait provides a convenient way to extract required fields from
/// protocol buffer messages with descriptive error messages.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::RequiredField;
///
/// let proto = wa_proto::MessageKey {
///     id: Some("msg123".to_string()),
///     remote_jid: None, // Missing required field
///     from_me: Some(true),
///     participant: None,
/// };
///
/// // This will return an error with field name
/// let result = proto.remote_jid.required("remote_jid");
/// assert!(result.is_err());
/// ```
trait RequiredField<T> {
    fn required(self, field_name: &str) -> Result<T, ProtoError>;
}

impl<T> RequiredField<T> for Option<T> {
    fn required(self, field_name: &str) -> Result<T, ProtoError> {
        self.ok_or_else(|| ProtoError::MissingRequiredField(field_name.to_string()))
    }
}

/// Macro for implementing bidirectional proto conversions with less boilerplate
///
/// # Examples
///
/// ```rust
/// impl_proto_conversion! {
///     MessageId => wa_proto::MessageKey {
///         from_proto: |proto| {
///             let id = proto.id.required("id")?;
///             Ok(MessageId::new(id))
///         },
///         to_proto: |self_param| {
///             wa_proto::MessageKey {
///                 remote_jid: None,
///                 from_me: None,
///                 id: Some(self_param.as_str().to_string()),
///                 participant: None,
///             }
///         }
///     }
/// }
/// ```
macro_rules! impl_proto_conversion {
    (
        $rust_type:ty => $proto_type:ty {
            from_proto: |$proto_param:ident| $from_body:expr,
            to_proto: |$self_param:ident| $to_body:expr $(,)?
        }
    ) => {
        impl FromProto<$proto_type> for $rust_type {
            type Error = ProtoError;

            fn from_proto($proto_param: $proto_type) -> Result<Self, Self::Error> {
                $from_body
            }
        }

        impl ToProto<$proto_type> for $rust_type {
            fn to_proto(&self) -> $proto_type {
                let $self_param = self;
                $to_body
            }
        }

        impl TryFrom<$proto_type> for $rust_type {
            type Error = ProtoError;

            fn try_from(proto: $proto_type) -> Result<Self, Self::Error> {
                FromProto::from_proto(proto)
            }
        }

        impl From<&$rust_type> for $proto_type {
            fn from(value: &$rust_type) -> Self {
                value.to_proto()
            }
        }

        impl From<$rust_type> for $proto_type {
            fn from(value: $rust_type) -> Self {
                value.to_proto()
            }
        }
    };
}

/// Converts protocol buffer types to Rust types with proper error handling.
///
/// This trait provides a standardized way to convert from protocol buffer
/// generated types to our internal Rust types, with comprehensive error
/// handling for missing fields and type conversion failures.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::FromProto;
/// use whatsmeow_types::MessageId;
///
/// let proto_key = wa_proto::MessageKey {
///     id: Some("msg123".to_string()),
///     remote_jid: Some("<EMAIL>".to_string()),
///     from_me: Some(true),
///     participant: None,
/// };
///
/// let message_id: MessageId = FromProto::from_proto(proto_key)?;
/// ```
///
/// # Error Handling
///
/// Implementations should return detailed errors for:
/// - Missing required fields
/// - Invalid data formats
/// - Type conversion failures
pub trait FromProto<T> {
    type Error;

    /// Convert from a protocol buffer type with error handling
    fn from_proto(proto: T) -> Result<Self, Self::Error>
    where
        Self: Sized;
}

/// Converts Rust types to protocol buffer types.
///
/// This trait provides a standardized way to convert from our internal Rust
/// types to protocol buffer generated types. Unlike `FromProto`, this trait
/// is infallible as we control the source data format.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::ToProto;
/// use whatsmeow_types::Jid;
///
/// let jid = Jid::new_unchecked(
///     "**********".to_string(),
///     "s.whatsapp.net".to_string(),
///     0
/// );
///
/// let proto: wa_proto::Account = jid.to_proto();
/// ```
pub trait ToProto<T> {
    /// Convert to a protocol buffer type
    fn to_proto(&self) -> T;
}

/// Trait for bidirectional conversion between Rust and protocol buffer types.
///
/// This trait is automatically implemented for any type that implements both
/// `FromProto<T>` and `ToProto<T>`, providing a convenient marker for types
/// that support full bidirectional conversion.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::{ProtoConvert, FromProto, ToProto};
/// use whatsmeow_types::Jid;
///
/// // Jid implements ProtoConvert<wa_proto::Account> automatically
/// fn roundtrip_conversion<T>(value: T) -> Result<T, T::Error>
/// where
///     T: ProtoConvert<wa_proto::Account> + Clone,
/// {
///     let proto = value.to_proto();
///     FromProto::from_proto(proto)
/// }
/// ```
pub trait ProtoConvert<T>: FromProto<T> + ToProto<T> {}

// Implement automatic ProtoConvert for types that implement both traits
impl<T, P> ProtoConvert<P> for T where T: FromProto<P> + ToProto<P> {}

// Core type conversions

// Use macro for MessageKey tuple conversion
impl_proto_conversion! {
    (String, bool, String, Option<String>) => wa_proto::MessageKey {
        from_proto: |proto| {
            let remote_jid = proto.remote_jid.required("remote_jid")?;
            let from_me = proto.from_me.unwrap_or(false);
            let id = proto.id.required("id")?;
            let participant = proto.participant;
            Ok((remote_jid, from_me, id, participant))
        },
        to_proto: |self_param| {
            wa_proto::MessageKey {
                remote_jid: Some(self_param.0.clone()),
                from_me: Some(self_param.1),
                id: Some(self_param.2.clone()),
                participant: self_param.3.clone(),
            }
        }
    }
}

// Convert MessageKey from proto to MessageId using macro
impl_proto_conversion! {
    MessageId => wa_proto::MessageKey {
        from_proto: |proto| {
            let id = proto.id.required("id")?;
            Ok(MessageId::new(id))
        },
        to_proto: |self_param| {
            wa_proto::MessageKey {
                remote_jid: None,
                from_me: None,
                id: Some(self_param.as_str().to_string()),
                participant: None,
            }
        }
    }
}

/// Convert JID parsing error to ProtoError
fn jid_error_to_proto_error(err: whatsmeow_types::jid::JidError) -> ProtoError {
    ProtoError::TypeConversionFailed(format!("JID parsing failed: {}", err))
}

// Convert Account from/to proto using macro
impl_proto_conversion! {
    Jid => wa_proto::Account {
        from_proto: |proto| {
            let username = proto.username.required("username")?;
            Jid::from_string(&username).map_err(jid_error_to_proto_error)
        },
        to_proto: |self_param| {
            AccountBuilder::new()
                .with_jid(self_param)
                .build()
        }
    }
}

/// Builder for Account proto with fluent interface
pub struct AccountBuilder {
    username: Option<String>,
    lid: Option<String>,
    country_code: Option<String>,
    is_username_deleted: Option<bool>,
}

impl AccountBuilder {
    pub fn new() -> Self {
        Self {
            username: None,
            lid: None,
            country_code: None,
            is_username_deleted: None,
        }
    }

    pub fn with_jid(mut self, jid: &Jid) -> Self {
        self.username = Some(jid.to_string());
        self
    }

    pub fn with_lid(mut self, lid: String) -> Self {
        self.lid = Some(lid);
        self
    }

    pub fn with_country_code(mut self, country_code: String) -> Self {
        self.country_code = Some(country_code);
        self
    }

    pub fn with_username_deleted(mut self, deleted: bool) -> Self {
        self.is_username_deleted = Some(deleted);
        self
    }

    pub fn build(self) -> wa_proto::Account {
        wa_proto::Account {
            username: self.username,
            lid: self.lid,
            country_code: self.country_code,
            is_username_deleted: self.is_username_deleted,
        }
    }
}

impl Default for AccountBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// From implementations are now automatically generated by the macro

// Message type conversions

/// Convert from proto message type enum to MessageType
///
/// Uses pattern matching for reliable conversion with clear mapping.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::proto_message_type_to_message_type;
/// use whatsmeow_types::message::MessageType;
///
/// let msg_type = proto_message_type_to_message_type(Some(1));
/// assert_eq!(msg_type, MessageType::Text);
///
/// let unknown = proto_message_type_to_message_type(Some(999));
/// assert_eq!(unknown, MessageType::Unknown);
/// ```
pub fn proto_message_type_to_message_type(proto_type: Option<i32>) -> MessageType {
    match proto_type {
        Some(1) => MessageType::Text,
        Some(2) => MessageType::Image,
        Some(3) => MessageType::Audio,
        Some(4) => MessageType::Video,
        Some(5) => MessageType::Document,
        Some(20) => MessageType::Contact,
        Some(22) => MessageType::Location,
        Some(28) => MessageType::Sticker,
        Some(45) => MessageType::Poll,
        _ => MessageType::Unknown,
    }
}

/// Convert MessageType to proto message type enum
///
/// Uses pattern matching for reliable conversion with clear mapping.
/// Returns `None` for message types that don't have proto equivalents.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::message_type_to_proto_message_type;
/// use whatsmeow_types::message::MessageType;
///
/// let proto_type = message_type_to_proto_message_type(&MessageType::Text);
/// assert_eq!(proto_type, Some(1));
///
/// let no_proto = message_type_to_proto_message_type(&MessageType::System);
/// assert_eq!(no_proto, None);
/// ```
pub fn message_type_to_proto_message_type(msg_type: &MessageType) -> Option<i32> {
    match msg_type {
        MessageType::Text => Some(1),
        MessageType::Image => Some(2),
        MessageType::Audio => Some(3),
        MessageType::Video => Some(4),
        MessageType::Document => Some(5),
        MessageType::Contact => Some(20),
        MessageType::Location => Some(22),
        MessageType::Sticker => Some(28),
        MessageType::Poll => Some(45),
        MessageType::System => None, // System messages don't have a proto type
        MessageType::Reaction => None, // Reactions are handled differently
        MessageType::Unknown => None,
    }
}

// Generic timestamp handling

use std::marker::PhantomData;
use std::time::Duration;

/// Marker type for seconds
pub struct Seconds;
/// Marker type for milliseconds  
pub struct Milliseconds;

/// Trait for time unit conversion
pub trait TimeUnit {
    fn to_duration(value: u64) -> Duration;
    fn from_duration(duration: Duration) -> u64;
}

impl TimeUnit for Seconds {
    fn to_duration(value: u64) -> Duration {
        Duration::from_secs(value)
    }

    fn from_duration(duration: Duration) -> u64 {
        duration.as_secs()
    }
}

impl TimeUnit for Milliseconds {
    fn to_duration(value: u64) -> Duration {
        Duration::from_millis(value)
    }

    fn from_duration(duration: Duration) -> u64 {
        duration.as_millis() as u64
    }
}

/// Generic timestamp wrapper with compile-time unit safety
pub struct Timestamp<T: TimeUnit> {
    value: u64,
    _phantom: PhantomData<T>,
}

impl<T: TimeUnit> Timestamp<T> {
    pub fn new(value: u64) -> Self {
        Self {
            value,
            _phantom: PhantomData,
        }
    }

    pub fn value(&self) -> u64 {
        self.value
    }

    pub fn to_system_time(self) -> SystemTime {
        UNIX_EPOCH + T::to_duration(self.value)
    }

    pub fn from_system_time(time: SystemTime) -> Self {
        let duration = time.duration_since(UNIX_EPOCH).unwrap_or_default();
        Self::new(T::from_duration(duration))
    }
}

/// Type aliases for convenience
pub type SecondTimestamp = Timestamp<Seconds>;
pub type MillisTimestamp = Timestamp<Milliseconds>;

// Helper functions for time conversion (backwards compatibility)

/// Helper function to convert timestamp to SystemTime
pub fn timestamp_to_system_time(timestamp: u64) -> SystemTime {
    SecondTimestamp::new(timestamp).to_system_time()
}

/// Helper function to convert SystemTime to timestamp
pub fn system_time_to_timestamp(time: SystemTime) -> u64 {
    SecondTimestamp::from_system_time(time).value()
}

/// Helper function to convert optional timestamp to SystemTime
pub fn optional_timestamp_to_system_time(timestamp: Option<u64>) -> Option<SystemTime> {
    timestamp.map(timestamp_to_system_time)
}

/// Helper function to convert optional SystemTime to timestamp
pub fn optional_system_time_to_timestamp(time: Option<SystemTime>) -> Option<u64> {
    time.map(system_time_to_timestamp)
}

/// Helper function to convert millisecond timestamp to SystemTime
pub fn millis_timestamp_to_system_time(timestamp: u64) -> SystemTime {
    MillisTimestamp::new(timestamp).to_system_time()
}

/// Helper function to convert SystemTime to millisecond timestamp
pub fn system_time_to_millis_timestamp(time: SystemTime) -> u64 {
    MillisTimestamp::from_system_time(time).value()
}

/// Helper function to convert optional millisecond timestamp to SystemTime
pub fn optional_millis_timestamp_to_system_time(timestamp: Option<u64>) -> Option<SystemTime> {
    timestamp.map(millis_timestamp_to_system_time)
}

/// Helper function to convert optional SystemTime to millisecond timestamp
pub fn optional_system_time_to_millis_timestamp(time: Option<SystemTime>) -> Option<u64> {
    time.map(system_time_to_millis_timestamp)
}

// Bytes conversion helpers

/// Convert bytes to base64 string
///
/// Uses the standard base64 engine which is optimized for general use cases.
/// For high-performance scenarios with large amounts of data, consider using
/// streaming base64 encoding.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::bytes_to_base64;
///
/// let data = b"Hello, World!";
/// let encoded = bytes_to_base64(data);
/// assert_eq!(encoded, "SGVsbG8sIFdvcmxkIQ==");
/// ```
///
/// # Performance
///
/// This function allocates a new String for the result. For repeated conversions,
/// consider reusing buffers when possible.
pub fn bytes_to_base64(bytes: &[u8]) -> String {
    use base64::Engine;
    base64::engine::general_purpose::STANDARD.encode(bytes)
}

/// Convert base64 string to bytes
///
/// Decodes a base64 string into a byte vector. Returns an error if the input
/// contains invalid base64 characters or padding.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::base64_to_bytes;
///
/// let encoded = "SGVsbG8sIFdvcmxkIQ==";
/// let decoded = base64_to_bytes(encoded).unwrap();
/// assert_eq!(decoded, b"Hello, World!");
/// ```
///
/// # Errors
///
/// Returns `ProtoError::TypeConversionFailed` if the input is not valid base64.
pub fn base64_to_bytes(base64_str: &str) -> Result<Vec<u8>, ProtoError> {
    use base64::Engine;
    base64::engine::general_purpose::STANDARD
        .decode(base64_str)
        .map_err(|e| ProtoError::TypeConversionFailed(format!("Base64 decode error: {}", e)))
}

/// Convert optional bytes to optional base64 string
///
/// Convenience function for handling optional byte slices. Returns `None` if
/// the input is `None`, otherwise encodes the bytes to base64.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::optional_bytes_to_base64;
///
/// let data = Some(b"test".as_slice());
/// let encoded = optional_bytes_to_base64(data);
/// assert_eq!(encoded, Some("dGVzdA==".to_string()));
///
/// let empty = optional_bytes_to_base64(None);
/// assert_eq!(empty, None);
/// ```
pub fn optional_bytes_to_base64(bytes: Option<&[u8]>) -> Option<String> {
    bytes.map(bytes_to_base64)
}

/// Convert optional base64 string to optional bytes
///
/// Convenience function for handling optional base64 strings. Returns `None` if
/// the input is `None`, otherwise attempts to decode the base64 string.
///
/// # Examples
///
/// ```rust
/// use whatsmeow_proto::conversion::optional_base64_to_bytes;
///
/// let encoded = Some("dGVzdA==");
/// let decoded = optional_base64_to_bytes(encoded).unwrap();
/// assert_eq!(decoded, Some(b"test".to_vec()));
///
/// let empty = optional_base64_to_bytes(None).unwrap();
/// assert_eq!(empty, None);
/// ```
///
/// # Errors
///
/// Returns `ProtoError::TypeConversionFailed` if the input contains invalid base64.
pub fn optional_base64_to_bytes(base64_str: Option<&str>) -> Result<Option<Vec<u8>>, ProtoError> {
    match base64_str {
        Some(s) => base64_to_bytes(s).map(Some),
        None => Ok(None),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::SystemTime;

    #[test]
    fn test_message_key_conversion() {
        let original = (
            "<EMAIL>".to_string(),
            true,
            "msg123".to_string(),
            Some("<EMAIL>".to_string()),
        );

        let proto = original.to_proto();
        let converted: (String, bool, String, Option<String>) =
            FromProto::from_proto(proto).unwrap();

        assert_eq!(original, converted);
    }

    #[test]
    fn test_message_key_to_message_id() {
        let proto = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test_message_id".to_string()),
            participant: None,
        };

        let message_id: MessageId = FromProto::from_proto(proto).unwrap();
        assert_eq!(message_id.as_str(), "test_message_id");
    }

    #[test]
    fn test_message_key_to_message_id_try_from() {
        let proto = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test_message_id_try_from".to_string()),
            participant: None,
        };

        let message_id: MessageId = proto.try_into().unwrap();
        assert_eq!(message_id.as_str(), "test_message_id_try_from");
    }

    #[test]
    fn test_message_key_missing_id() {
        let proto = wa_proto::MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: None, // Missing required field
            participant: None,
        };

        let result: Result<MessageId, ProtoError> = FromProto::from_proto(proto);
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::MissingRequiredField(_)
        ));
    }

    #[test]
    fn test_message_id_to_proto_conversion() {
        let message_id = MessageId::new("test_msg_123".to_string());
        let proto = message_id.to_proto();

        assert_eq!(proto.id, Some("test_msg_123".to_string()));
        assert_eq!(proto.remote_jid, None);
        assert_eq!(proto.from_me, None);
        assert_eq!(proto.participant, None);
    }

    #[test]
    fn test_account_to_jid_conversion() {
        let proto = wa_proto::Account {
            username: Some("<EMAIL>".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let jid: Jid = FromProto::from_proto(proto).unwrap();
        assert_eq!(jid.user, "**********");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 0);
    }

    #[test]
    fn test_account_to_jid_with_device() {
        let proto = wa_proto::Account {
            username: Some("<EMAIL>.1".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let jid: Jid = FromProto::from_proto(proto).unwrap();
        assert_eq!(jid.user, "**********");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 1);
    }

    #[test]
    fn test_account_to_jid_try_from() {
        let proto = wa_proto::Account {
            username: Some("<EMAIL>.2".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let jid: Jid = proto.try_into().unwrap();
        assert_eq!(jid.user, "**********");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 2);
    }

    #[test]
    fn test_jid_to_account_conversion() {
        let jid = Jid::new_unchecked("**********".to_string(), "s.whatsapp.net".to_string(), 0);
        let proto = jid.to_proto();

        assert_eq!(
            proto.username,
            Some("<EMAIL>".to_string())
        );
    }

    #[test]
    fn test_jid_to_account_from_trait() {
        let jid = Jid::new_unchecked("**********".to_string(), "s.whatsapp.net".to_string(), 0);
        let proto: wa_proto::Account = (&jid).into();

        assert_eq!(
            proto.username,
            Some("<EMAIL>".to_string())
        );

        let proto2: wa_proto::Account = jid.into();
        assert_eq!(
            proto2.username,
            Some("<EMAIL>".to_string())
        );
    }

    #[test]
    fn test_jid_to_account_with_device() {
        let jid = Jid::new_unchecked("**********".to_string(), "s.whatsapp.net".to_string(), 2);
        let proto = jid.to_proto();

        assert_eq!(
            proto.username,
            Some("<EMAIL>.2".to_string())
        );
    }

    #[test]
    fn test_message_type_conversions() {
        let test_cases = vec![
            (MessageType::Text, Some(1)),
            (MessageType::Image, Some(2)),
            (MessageType::Audio, Some(3)),
            (MessageType::Video, Some(4)),
            (MessageType::Document, Some(5)),
            (MessageType::Contact, Some(20)),
            (MessageType::Location, Some(22)),
            (MessageType::Sticker, Some(28)),
            (MessageType::Poll, Some(45)),
            (MessageType::System, None),
            (MessageType::Reaction, None),
            (MessageType::Unknown, None),
        ];

        for (msg_type, expected_proto) in test_cases {
            let proto_type = message_type_to_proto_message_type(&msg_type);
            assert_eq!(proto_type, expected_proto);

            if let Some(proto_val) = expected_proto {
                let converted_back = proto_message_type_to_message_type(Some(proto_val));
                assert_eq!(converted_back, msg_type);
            }
        }
    }

    #[test]
    fn test_timestamp_conversions() {
        let now = SystemTime::now();
        let timestamp = system_time_to_timestamp(now);
        let converted_back = timestamp_to_system_time(timestamp);

        // Allow for small differences due to precision loss
        let diff = now
            .duration_since(converted_back)
            .or_else(|_| converted_back.duration_since(now))
            .unwrap();
        assert!(diff.as_secs() <= 1);
    }

    #[test]
    fn test_millis_timestamp_conversions() {
        let now = SystemTime::now();
        let timestamp = system_time_to_millis_timestamp(now);
        let converted_back = millis_timestamp_to_system_time(timestamp);

        // Allow for small differences due to precision loss
        let diff = now
            .duration_since(converted_back)
            .or_else(|_| converted_back.duration_since(now))
            .unwrap();
        assert!(diff.as_millis() <= 1);
    }

    #[test]
    fn test_generic_timestamp_types() {
        let sec_timestamp = SecondTimestamp::new(1640995200);
        let millis_timestamp = MillisTimestamp::new(1640995200000);

        let sec_system_time = sec_timestamp.to_system_time();
        let millis_system_time = millis_timestamp.to_system_time();

        // Both should represent the same time
        let diff = sec_system_time
            .duration_since(millis_system_time)
            .or_else(|_| millis_system_time.duration_since(sec_system_time))
            .unwrap();
        assert!(diff.as_millis() <= 1);

        // Test round-trip conversion
        let sec_roundtrip = SecondTimestamp::from_system_time(sec_system_time);
        assert_eq!(sec_roundtrip.value(), 1640995200);

        let millis_roundtrip = MillisTimestamp::from_system_time(millis_system_time);
        assert_eq!(millis_roundtrip.value(), 1640995200000);
    }

    #[test]
    fn test_optional_timestamp_conversions() {
        let now = Some(SystemTime::now());
        let timestamp = optional_system_time_to_timestamp(now);
        let converted_back = optional_timestamp_to_system_time(timestamp);

        assert!(converted_back.is_some());
        if let (Some(original), Some(converted)) = (now, converted_back) {
            let diff = original
                .duration_since(converted)
                .or_else(|_| converted.duration_since(original))
                .unwrap();
            assert!(diff.as_secs() <= 1);
        }

        // Test None case
        assert_eq!(optional_system_time_to_timestamp(None), None);
        assert_eq!(optional_timestamp_to_system_time(None), None);
    }

    #[test]
    fn test_base64_conversions() {
        let test_data = b"Hello, World!";
        let base64_str = bytes_to_base64(test_data);
        let converted_back = base64_to_bytes(&base64_str).unwrap();

        assert_eq!(test_data, converted_back.as_slice());
    }

    #[test]
    fn test_optional_base64_conversions() {
        let test_data = b"Test data";
        let base64_str = optional_bytes_to_base64(Some(test_data));
        let converted_back = optional_base64_to_bytes(base64_str.as_deref()).unwrap();

        assert_eq!(Some(test_data.to_vec()), converted_back);

        // Test None case
        assert_eq!(optional_bytes_to_base64(None), None);
        assert_eq!(optional_base64_to_bytes(None).unwrap(), None);
    }

    #[test]
    fn test_invalid_base64() {
        let invalid_base64 = "invalid base64!@#$";
        let result = base64_to_bytes(invalid_base64);
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::TypeConversionFailed(_)
        ));
    }

    #[test]
    fn test_invalid_jid_format_in_proto() {
        let proto = wa_proto::Account {
            username: Some("invalid_jid_format".to_string()), // No @ symbol
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let result: Result<Jid, ProtoError> = FromProto::from_proto(proto);
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::TypeConversionFailed(_)
        ));
    }

    #[test]
    fn test_empty_username_in_proto() {
        let proto = wa_proto::Account {
            username: Some("".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let result: Result<Jid, ProtoError> = FromProto::from_proto(proto);
        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            ProtoError::TypeConversionFailed(_)
        ));
    }

    #[test]
    fn test_jid_roundtrip_conversion() {
        let test_cases = vec![
            ("**********", "s.whatsapp.net", 0),
            ("user", "example.com", 1),
            ("test.user", "g.us", 255),
        ];

        for (user, server, device) in test_cases {
            let original_jid = Jid::new_unchecked(user.to_string(), server.to_string(), device);

            // Convert to proto and back
            let proto = original_jid.to_proto();
            let converted_jid: Jid = FromProto::from_proto(proto).unwrap();

            assert_eq!(original_jid.user, converted_jid.user);
            assert_eq!(original_jid.server, converted_jid.server);
            assert_eq!(original_jid.device, converted_jid.device);
        }
    }

    #[test]
    fn test_jid_parsing_integration() {
        // Test that conversion.rs properly uses jid.rs parsing
        let proto = wa_proto::Account {
            username: Some("<EMAIL>".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let jid: Jid = FromProto::from_proto(proto).unwrap();
        assert_eq!(jid.user, "**********");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 0);
    }

    #[test]
    fn test_jid_parsing_with_device_integration() {
        let proto = wa_proto::Account {
            username: Some("<EMAIL>.1".to_string()),
            lid: None,
            country_code: None,
            is_username_deleted: None,
        };

        let jid: Jid = FromProto::from_proto(proto).unwrap();
        assert_eq!(jid.user, "**********");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 1);
    }

    #[test]
    fn test_account_builder_fluent_interface() {
        let account = AccountBuilder::new()
            .with_jid(&Jid::new_unchecked(
                "**********".to_string(),
                "s.whatsapp.net".to_string(),
                0,
            ))
            .with_country_code("US".to_string())
            .with_username_deleted(false)
            .build();

        assert_eq!(
            account.username,
            Some("<EMAIL>".to_string())
        );
        assert_eq!(account.country_code, Some("US".to_string()));
        assert_eq!(account.is_username_deleted, Some(false));
        assert_eq!(account.lid, None);
    }
}
