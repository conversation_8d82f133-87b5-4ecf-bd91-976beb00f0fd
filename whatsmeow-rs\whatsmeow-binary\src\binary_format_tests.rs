//! Binary format compatibility tests to ensure exact Go implementation matching

use crate::node::{Node, NodeContent};
use crate::token;

#[test]
fn test_list_size_boundaries() {
    // Test LIST_8 boundary (255 items)
    let mut large_node = Node::new("test");
    for i in 0..127 {
        // 127 attributes = 254 items + tag = 255 total
        large_node.set_attribute(format!("attr{}", i), format!("value{}", i));
    }

    let serialized = large_node.serialize().expect("Should serialize large node");
    let deserialized = Node::parse(&serialized).expect("Should deserialize large node");

    assert_eq!(deserialized.tag, "test");
    assert_eq!(deserialized.attributes.len(), 127);

    // Test LIST_16 boundary (would need >255 items)
    // This is impractical to test with attributes, but we can test with children
    let mut children = Vec::new();
    for i in 0..300 {
        children.push(Node::with_text(
            format!("child{}", i),
            format!("content{}", i),
        ));
    }

    let large_children_node = Node::with_children("parent", children);
    let serialized2 = large_children_node
        .serialize()
        .expect("Should serialize large children");
    let deserialized2 = Node::parse(&serialized2).expect("Should deserialize large children");

    assert_eq!(deserialized2.tag, "parent");
    let parsed_children = deserialized2.children().expect("Should have children");
    assert_eq!(parsed_children.len(), 300);
}

#[test]
fn test_binary_length_boundaries() {
    // Test BINARY_8 boundary (255 bytes)
    let data_255 = vec![0xAA; 255];
    let node1 = Node::with_binary("test", data_255.clone());
    let serialized1 = node1.serialize().expect("Should serialize 255 bytes");
    let deserialized1 = Node::parse(&serialized1).expect("Should deserialize 255 bytes");
    assert_eq!(deserialized1.binary(), Some(&data_255));

    // Test BINARY_20 boundary (256 bytes - should use BINARY_20)
    let data_256 = vec![0xBB; 256];
    let node2 = Node::with_binary("test", data_256.clone());
    let serialized2 = node2.serialize().expect("Should serialize 256 bytes");
    let deserialized2 = Node::parse(&serialized2).expect("Should deserialize 256 bytes");
    assert_eq!(deserialized2.binary(), Some(&data_256));

    // Test BINARY_20 maximum (1048575 bytes)
    let data_large = vec![0xCC; 65536]; // 64KB
    let node3 = Node::with_binary("test", data_large.clone());
    let serialized3 = node3.serialize().expect("Should serialize 64KB");
    let deserialized3 = Node::parse(&serialized3).expect("Should deserialize 64KB");
    assert_eq!(deserialized3.binary(), Some(&data_large));
}

#[test]
fn test_token_encoding_precedence() {
    // Test that single-byte tokens take precedence over double-byte
    let single_byte_tokens = vec!["type", "id", "from", "to", "message"];

    for token in single_byte_tokens {
        let node = Node::with_text("test", token);
        let serialized = node.serialize().expect("Should serialize");

        // Verify it's using single-byte encoding (should be compact)
        // Single byte token should result in smaller serialization
        assert!(
            serialized.len() < 20,
            "Single byte token should be compact: {}",
            token
        );

        let deserialized = Node::parse(&serialized).expect("Should deserialize");
        assert_eq!(deserialized.text(), Some(&token.to_string()));
    }
}

#[test]
fn test_packed_string_precedence() {
    // Test that packed strings take precedence over raw strings
    let nibble_string = "123456789";
    let hex_string = "ABCDEF123";

    // Nibble string should be packed
    let node1 = Node::with_text("test", nibble_string);
    let serialized1 = node1.serialize().expect("Should serialize nibble");
    let deserialized1 = Node::parse(&serialized1).expect("Should deserialize nibble");
    assert_eq!(deserialized1.text(), Some(&nibble_string.to_string()));

    // Hex string should be packed
    let node2 = Node::with_text("test", hex_string);
    let serialized2 = node2.serialize().expect("Should serialize hex");
    let deserialized2 = Node::parse(&serialized2).expect("Should deserialize hex");
    assert_eq!(deserialized2.text(), Some(&hex_string.to_string()));

    // Mixed string should use raw encoding
    let mixed_string = "Hello123World";
    let node3 = Node::with_text("test", mixed_string);
    let serialized3 = node3.serialize().expect("Should serialize mixed");
    let deserialized3 = Node::parse(&serialized3).expect("Should deserialize mixed");
    assert_eq!(deserialized3.text(), Some(&mixed_string.to_string()));
}

#[test]
fn test_attribute_ordering_consistency() {
    // Test that attribute ordering is consistent (though order may not be preserved)
    let node = Node::new("test")
        .with_attribute("z_last", "value1")
        .with_attribute("a_first", "value2")
        .with_attribute("m_middle", "value3");

    let serialized = node.serialize().expect("Should serialize");
    let deserialized = Node::parse(&serialized).expect("Should deserialize");

    // All attributes should be present regardless of order
    assert_eq!(deserialized.attributes.len(), 3);
    assert!(deserialized.has_attribute("z_last"));
    assert!(deserialized.has_attribute("a_first"));
    assert!(deserialized.has_attribute("m_middle"));
}

#[test]
fn test_content_type_detection() {
    // Test that content type is correctly detected during parsing

    // Pure binary data (non-UTF8)
    let binary_data = vec![0xFF, 0xFE, 0xFD, 0x00, 0x01];
    let binary_node = Node::with_binary("test", binary_data.clone());
    let serialized_binary = binary_node.serialize().expect("Should serialize binary");
    let deserialized_binary = Node::parse(&serialized_binary).expect("Should deserialize binary");

    match &deserialized_binary.content {
        NodeContent::Binary(data) => assert_eq!(data, &binary_data),
        NodeContent::Text(_) => panic!("Binary data should not be interpreted as text"),
        _ => panic!("Unexpected content type"),
    }

    // UTF-8 text data
    let text_data = "Hello, 世界! 🌍";
    let text_node = Node::with_text("test", text_data);
    let serialized_text = text_node.serialize().expect("Should serialize text");
    let deserialized_text = Node::parse(&serialized_text).expect("Should deserialize text");

    match &deserialized_text.content {
        NodeContent::Text(text) => assert_eq!(text, text_data),
        NodeContent::Binary(_) => panic!("Text data should not be interpreted as binary"),
        _ => panic!("Unexpected content type"),
    }
}

#[test]
fn test_nested_list_structures() {
    // Test deeply nested list structures
    let leaf = Node::with_text("leaf", "value");
    let mut current = leaf;

    // Create 10 levels of nesting
    for level in (0..10).rev() {
        current = Node::with_children(format!("level{}", level), vec![current]);
    }

    let serialized = current
        .serialize()
        .expect("Should serialize nested structure");
    let deserialized = Node::parse(&serialized).expect("Should deserialize nested structure");

    // Navigate through the structure
    let mut nav = &deserialized;
    for level in 0..10 {
        assert_eq!(nav.tag, format!("level{}", level));
        let children = nav.children().expect("Should have children");
        assert_eq!(children.len(), 1);
        nav = &children[0];
    }
    assert_eq!(nav.tag, "leaf");
    assert_eq!(nav.text(), Some(&"value".to_string()));
}

#[test]
fn test_empty_vs_null_handling() {
    // Test distinction between empty and null values
    // Note: Empty string content is removed due to binary format ambiguity
    // The format cannot distinguish between empty text and empty binary data

    // No content
    let no_content = Node::new("test");
    let serialized_none = no_content.serialize().expect("Should serialize no content");
    let deserialized_none = Node::parse(&serialized_none).expect("Should deserialize no content");
    assert_eq!(deserialized_none.content, NodeContent::None);

    // Empty binary
    let empty_binary = Node::with_binary("test", vec![]);
    let serialized_empty_bin = empty_binary
        .serialize()
        .expect("Should serialize empty binary");
    let deserialized_empty_bin =
        Node::parse(&serialized_empty_bin).expect("Should deserialize empty binary");
    assert_eq!(deserialized_empty_bin.binary(), Some(&vec![]));

    // Empty children list
    let empty_children = Node::with_children("test", vec![]);
    let serialized_empty_children = empty_children
        .serialize()
        .expect("Should serialize empty children");
    let deserialized_empty_children =
        Node::parse(&serialized_empty_children).expect("Should deserialize empty children");
    assert_eq!(deserialized_empty_children.children(), Some(&vec![]));
}

#[test]
fn test_special_byte_values() {
    // Test handling of special byte values that might conflict with tokens
    let special_bytes = vec![
        vec![0x00],              // NULL
        vec![0xFF],              // Max byte value
        vec![token::LIST_EMPTY], // LIST_EMPTY token value
        vec![token::LIST_8],     // LIST_8 token value
        vec![token::BINARY_8],   // BINARY_8 token value
        vec![token::NIBBLE_8],   // NIBBLE_8 token value
        vec![token::HEX_8],      // HEX_8 token value
    ];

    for special_data in special_bytes {
        let node = Node::with_binary("test", special_data.clone());
        let serialized = node.serialize().expect("Should serialize special bytes");
        let deserialized = Node::parse(&serialized).expect("Should deserialize special bytes");

        // Should be preserved as binary data
        match &deserialized.content {
            NodeContent::Binary(data) => assert_eq!(data, &special_data),
            _ => panic!(
                "Special bytes should be preserved as binary: {:?}",
                special_data
            ),
        }
    }
}

#[test]
fn test_unicode_normalization() {
    // Test Unicode normalization consistency
    let unicode_variants = vec![
        "café",         // NFC
        "cafe\u{0301}", // NFD (e + combining acute)
        "naïve",
        "naı\u{0308}ve", // i + combining diaeresis
    ];

    for unicode_text in unicode_variants {
        let node = Node::with_text("test", unicode_text);
        let serialized = node.serialize().expect("Should serialize Unicode");
        let deserialized = Node::parse(&serialized).expect("Should deserialize Unicode");

        // Unicode should be preserved exactly as input
        assert_eq!(deserialized.text(), Some(&unicode_text.to_string()));
    }
}

#[test]
fn test_large_string_handling() {
    // Test very large strings that exceed normal limits
    let large_string = "A".repeat(100000); // 100KB string
    let node = Node::with_text("test", &large_string);
    let serialized = node.serialize().expect("Should serialize large string");
    let deserialized = Node::parse(&serialized).expect("Should deserialize large string");
    assert_eq!(deserialized.text(), Some(&large_string));
}

#[test]
fn test_mixed_content_siblings() {
    // Test siblings with different content types
    let text_child = Node::with_text("text", "Hello");
    let binary_child = Node::with_binary("binary", vec![1, 2, 3, 4]);
    let empty_child = Node::new("empty");
    let nested_child = Node::with_children("nested", vec![Node::with_text("inner", "World")]);

    let parent = Node::with_children(
        "parent",
        vec![text_child, binary_child, empty_child, nested_child],
    );

    let serialized = parent.serialize().expect("Should serialize mixed siblings");
    let deserialized = Node::parse(&serialized).expect("Should deserialize mixed siblings");

    let children = deserialized.children().expect("Should have children");
    assert_eq!(children.len(), 4);

    // Verify each child type
    assert_eq!(children[0].text(), Some(&"Hello".to_string()));
    assert_eq!(children[1].binary(), Some(&vec![1, 2, 3, 4]));
    assert_eq!(children[2].content, NodeContent::None);

    let nested_children = children[3].children().expect("Should have nested children");
    assert_eq!(nested_children.len(), 1);
    assert_eq!(nested_children[0].text(), Some(&"World".to_string()));
}

#[test]
fn test_serialization_determinism() {
    // Test that serialization is deterministic (same input -> same output)
    let mut node = Node::new("test")
        .with_attribute("attr1", "value1")
        .with_attribute("attr2", "value2");
    node.set_text("content");

    let serialized1 = node.serialize().expect("Should serialize");
    let serialized2 = node.serialize().expect("Should serialize again");

    // Should produce identical output
    assert_eq!(
        serialized1, serialized2,
        "Serialization should be deterministic"
    );
}

#[test]
fn test_roundtrip_fidelity() {
    // Test that multiple roundtrips preserve data exactly
    let original = Node::new("complex")
        .with_attribute("id", "test-123")
        .with_attribute("type", "mixed")
        .with_attribute("count", 42i64)
        .with_attribute("enabled", true);

    let child1 = Node::with_text("text", "Hello, World! 🌍");
    let child2 = Node::with_binary("data", vec![0x00, 0xFF, 0x7F, 0x80]);

    let complex_node = Node {
        tag: original.tag,
        attributes: original.attributes,
        content: NodeContent::Children(vec![child1, child2]),
    };

    // Perform multiple roundtrips
    let mut current_data = complex_node.serialize().expect("Should serialize");

    for i in 0..5 {
        let parsed =
            Node::parse(&current_data).unwrap_or_else(|_| panic!("Should parse iteration {}", i));
        current_data = parsed
            .serialize()
            .unwrap_or_else(|_| panic!("Should serialize iteration {}", i));
    }

    // Final parse should match original structure
    let final_node = Node::parse(&current_data).expect("Should parse final");
    assert_eq!(final_node.tag, "complex");
    assert_eq!(final_node.attributes.len(), 4);

    let final_children = final_node.children().expect("Should have children");
    assert_eq!(final_children.len(), 2);
    assert_eq!(
        final_children[0].text(),
        Some(&"Hello, World! 🌍".to_string())
    );
    assert_eq!(
        final_children[1].binary(),
        Some(&vec![0x00, 0xFF, 0x7F, 0x80])
    );
}
