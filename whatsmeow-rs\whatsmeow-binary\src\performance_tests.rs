//! Performance and stress tests to ensure robust behavior under load

use crate::node::{Node, NodeContent};
use std::time::Instant;

#[test]
fn test_large_node_performance() {
    // Test performance with large nodes
    let start = Instant::now();

    let mut large_node = Node::new("large_test");
    for i in 0..1000 {
        large_node.set_attribute(format!("attr_{}", i), format!("value_{}", i));
    }

    let serialize_start = Instant::now();
    let serialized = large_node.serialize().expect("Should serialize large node");
    let serialize_duration = serialize_start.elapsed();

    let parse_start = Instant::now();
    let deserialized = Node::parse(&serialized).expect("Should deserialize large node");
    let parse_duration = parse_start.elapsed();

    let total_duration = start.elapsed();

    // Verify correctness
    assert_eq!(deserialized.tag, "large_test");
    assert_eq!(deserialized.attributes.len(), 1000);

    // Performance should be reasonable (these are loose bounds)
    assert!(
        serialize_duration.as_millis() < 1000,
        "Serialization took too long: {:?}",
        serialize_duration
    );
    assert!(
        parse_duration.as_millis() < 1000,
        "Parsing took too long: {:?}",
        parse_duration
    );
    assert!(
        total_duration.as_millis() < 2000,
        "Total operation took too long: {:?}",
        total_duration
    );

    println!(
        "Large node performance: serialize={:?}, parse={:?}, total={:?}",
        serialize_duration, parse_duration, total_duration
    );
}

#[test]
fn test_deep_nesting_performance() {
    // Test performance with deeply nested structures
    let start = Instant::now();

    let mut current = Node::with_text("leaf", "deep_value");
    for i in (0..100).rev() {
        current = Node::with_children(format!("level_{}", i), vec![current]);
    }

    let serialize_start = Instant::now();
    let serialized = current
        .serialize()
        .expect("Should serialize deep structure");
    let serialize_duration = serialize_start.elapsed();

    let parse_start = Instant::now();
    let deserialized = Node::parse(&serialized).expect("Should deserialize deep structure");
    let parse_duration = parse_start.elapsed();

    let total_duration = start.elapsed();

    // Verify correctness by navigating to leaf
    let mut nav = &deserialized;
    for i in 0..100 {
        assert_eq!(nav.tag, format!("level_{}", i));
        let children = nav.children().expect("Should have children");
        assert_eq!(children.len(), 1);
        nav = &children[0];
    }
    assert_eq!(nav.tag, "leaf");
    assert_eq!(nav.text(), Some(&"deep_value".to_string()));

    // Performance should be reasonable
    assert!(
        serialize_duration.as_millis() < 500,
        "Deep serialization took too long: {:?}",
        serialize_duration
    );
    assert!(
        parse_duration.as_millis() < 500,
        "Deep parsing took too long: {:?}",
        parse_duration
    );

    println!(
        "Deep nesting performance: serialize={:?}, parse={:?}, total={:?}",
        serialize_duration, parse_duration, total_duration
    );
}

#[test]
fn test_wide_structure_performance() {
    // Test performance with wide structures (many siblings)
    let start = Instant::now();

    let mut children = Vec::new();
    for i in 0..1000 {
        children.push(Node::with_text(
            format!("child_{}", i),
            format!("content_{}", i),
        ));
    }

    let wide_node = Node::with_children("wide_parent", children);

    let serialize_start = Instant::now();
    let serialized = wide_node
        .serialize()
        .expect("Should serialize wide structure");
    let serialize_duration = serialize_start.elapsed();

    let parse_start = Instant::now();
    let deserialized = Node::parse(&serialized).expect("Should deserialize wide structure");
    let parse_duration = parse_start.elapsed();

    let total_duration = start.elapsed();

    // Verify correctness
    assert_eq!(deserialized.tag, "wide_parent");
    let parsed_children = deserialized.children().expect("Should have children");
    assert_eq!(parsed_children.len(), 1000);

    for (i, child) in parsed_children.iter().enumerate() {
        assert_eq!(child.tag, format!("child_{}", i));
        assert_eq!(child.text(), Some(&format!("content_{}", i)));
    }

    // Performance should be reasonable
    assert!(
        serialize_duration.as_millis() < 1000,
        "Wide serialization took too long: {:?}",
        serialize_duration
    );
    assert!(
        parse_duration.as_millis() < 1000,
        "Wide parsing took too long: {:?}",
        parse_duration
    );

    println!(
        "Wide structure performance: serialize={:?}, parse={:?}, total={:?}",
        serialize_duration, parse_duration, total_duration
    );
}

#[test]
fn test_large_binary_data_performance() {
    // Test performance with large binary data
    let data_sizes = vec![1024, 10240, 102400, 1048576]; // 1KB, 10KB, 100KB, 1MB

    for size in data_sizes {
        let start = Instant::now();

        let large_data = vec![0xAA; size];
        let binary_node = Node::with_binary("binary_test", large_data.clone());

        let serialize_start = Instant::now();
        let serialized = binary_node
            .serialize()
            .expect("Should serialize binary data");
        let serialize_duration = serialize_start.elapsed();

        let parse_start = Instant::now();
        let deserialized = Node::parse(&serialized).expect("Should deserialize binary data");
        let parse_duration = parse_start.elapsed();

        let _total_duration = start.elapsed();

        // Verify correctness
        assert_eq!(deserialized.binary(), Some(&large_data));

        // Performance should scale reasonably with size
        let size_mb = size as f64 / 1048576.0;
        let serialize_rate = size_mb / serialize_duration.as_secs_f64();
        let parse_rate = size_mb / parse_duration.as_secs_f64();

        println!(
            "Binary data performance ({}KB): serialize={:?} ({:.1}MB/s), parse={:?} ({:.1}MB/s)",
            size / 1024,
            serialize_duration,
            serialize_rate,
            parse_duration,
            parse_rate
        );

        // Should achieve reasonable throughput (at least 10MB/s)
        assert!(
            serialize_rate > 10.0 || size < 102400,
            "Serialization rate too slow: {:.1}MB/s for {}KB",
            serialize_rate,
            size / 1024
        );
        assert!(
            parse_rate > 10.0 || size < 102400,
            "Parse rate too slow: {:.1}MB/s for {}KB",
            parse_rate,
            size / 1024
        );
    }
}

#[test]
fn test_repeated_operations_performance() {
    // Test performance of repeated serialize/deserialize operations
    let test_node = Node::new("test")
        .with_attribute("id", "perf-test-123")
        .with_attribute("type", "performance")
        .with_attribute("count", 42i64);

    let child1 = Node::with_text("text", "Hello, World!");
    let child2 = Node::with_binary("data", vec![1, 2, 3, 4, 5]);

    let complex_node = Node {
        tag: test_node.tag,
        attributes: test_node.attributes,
        content: NodeContent::Children(vec![child1, child2]),
    };

    let iterations = 1000;
    let start = Instant::now();

    for i in 0..iterations {
        let serialized = complex_node.serialize().expect("Should serialize");
        let deserialized = Node::parse(&serialized).expect("Should deserialize");

        // Verify correctness occasionally
        if i % 100 == 0 {
            assert_eq!(deserialized.tag, "test");
            assert_eq!(deserialized.attributes.len(), 3);
            let children = deserialized.children().expect("Should have children");
            assert_eq!(children.len(), 2);
        }
    }

    let total_duration = start.elapsed();
    let ops_per_second = iterations as f64 / total_duration.as_secs_f64();

    println!(
        "Repeated operations: {} iterations in {:?} ({:.1} ops/sec)",
        iterations, total_duration, ops_per_second
    );

    // Should achieve reasonable throughput
    assert!(
        ops_per_second > 100.0,
        "Operations per second too low: {:.1}",
        ops_per_second
    );
}

#[test]
fn test_memory_usage_stability() {
    // Test that memory usage doesn't grow excessively during operations
    let _base_node = Node::new("memory_test").with_attribute("test", "memory_stability");

    // Perform many operations to check for memory leaks
    for batch in 0..10 {
        let mut nodes = Vec::new();

        // Create many nodes
        for i in 0..100 {
            let mut node = Node::new(format!("node_{}_{}", batch, i))
                .with_attribute("batch", batch.to_string())
                .with_attribute("index", i.to_string());
            node.set_text(format!("content_{}_{}", batch, i));

            let serialized = node.serialize().expect("Should serialize");
            let deserialized = Node::parse(&serialized).expect("Should deserialize");
            nodes.push(deserialized);
        }

        // Verify some nodes
        assert_eq!(nodes.len(), 100);
        assert_eq!(nodes[0].tag, format!("node_{}_0", batch));
        assert_eq!(nodes[99].tag, format!("node_{}_99", batch));

        // Let nodes go out of scope to test cleanup
    }

    // If we get here without running out of memory, the test passes
    println!("Memory stability test completed successfully");
}

#[test]
fn test_concurrent_operations() {
    // Test concurrent serialize/deserialize operations
    use std::sync::Arc;
    use std::thread;

    let test_data = Arc::new(vec![
        Node::with_text("concurrent_test_1", "Hello"),
        Node::with_binary("concurrent_test_2", vec![1, 2, 3, 4]),
        Node::with_children(
            "concurrent_test_3",
            vec![Node::with_text("child", "nested")],
        ),
    ]);

    let mut handles = vec![];
    let start = Instant::now();

    // Spawn multiple threads
    for thread_id in 0..4 {
        let data_clone: Arc<Vec<Node>> = Arc::clone(&test_data);

        let handle = thread::spawn(move || {
            let mut results = Vec::new();

            for iteration in 0..250 {
                // 250 * 4 = 1000 total operations
                for (node_idx, node) in data_clone.iter().enumerate() {
                    let serialized = node.serialize().expect("Should serialize");
                    let deserialized = Node::parse(&serialized).expect("Should deserialize");

                    // Verify correctness
                    assert_eq!(deserialized.tag, node.tag);

                    results.push((thread_id, iteration, node_idx, deserialized.tag.clone()));
                }
            }

            results
        });

        handles.push(handle);
    }

    // Collect results
    let mut all_results = Vec::new();
    for handle in handles {
        let thread_results = handle.join().unwrap();
        all_results.extend(thread_results);
    }

    let total_duration = start.elapsed();
    let total_operations = all_results.len();
    let ops_per_second = total_operations as f64 / total_duration.as_secs_f64();

    println!(
        "Concurrent operations: {} operations across 4 threads in {:?} ({:.1} ops/sec)",
        total_operations, total_duration, ops_per_second
    );

    // Verify all operations completed successfully
    assert_eq!(total_operations, 3000); // 4 threads * 250 iterations * 3 nodes

    // Should achieve reasonable concurrent throughput
    assert!(
        ops_per_second > 500.0,
        "Concurrent ops per second too low: {:.1}",
        ops_per_second
    );
}

#[test]
fn test_token_lookup_performance() {
    // Test performance of token lookup operations
    let common_tokens = vec![
        "message",
        "type",
        "id",
        "from",
        "to",
        "body",
        "iq",
        "presence",
        "s.whatsapp.net",
        "g.us",
        "receipt",
        "notification",
        "error",
    ];

    let iterations = 10000;
    let start = Instant::now();

    for _ in 0..iterations {
        for token in &common_tokens {
            // Test both single and double byte token lookups
            let _single = crate::token::find_single_token(token);
            let _double = crate::token::find_double_token(token);

            // Create nodes with these tokens
            let node = Node::with_text("test", *token);
            let _serialized = node.serialize().expect("Should serialize");
        }
    }

    let total_duration = start.elapsed();
    let total_lookups = iterations * common_tokens.len() * 3; // 3 operations per token
    let lookups_per_second = total_lookups as f64 / total_duration.as_secs_f64();

    println!(
        "Token lookup performance: {} lookups in {:?} ({:.0} lookups/sec)",
        total_lookups, total_duration, lookups_per_second
    );

    // Token lookups should be very fast
    assert!(
        lookups_per_second > 100000.0,
        "Token lookup rate too slow: {:.0}/sec",
        lookups_per_second
    );
}
