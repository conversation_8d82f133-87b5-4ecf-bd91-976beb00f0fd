[package]
name = "whatsmeow-store"
version = "0.1.0"
edition = "2021"
description = "Storage interfaces and implementations for WhatsApp Web client"
license = "MIT"

[dependencies]
whatsmeow-types = { path = "../whatsmeow-types" }

async-trait = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }

[dev-dependencies]
tokio-test = { workspace = true }
mockall = { workspace = true }
tempfile = "3.8"