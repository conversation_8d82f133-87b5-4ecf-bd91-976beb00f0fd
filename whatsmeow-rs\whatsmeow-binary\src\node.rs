//! Binary node representation

use crate::error::BinaryError;
use bytes::Bytes;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Binary node structure for WhatsApp protocol
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Node {
    pub tag: String,
    pub attributes: HashMap<String, AttributeValue>,
    pub content: NodeContent,
}

/// Attribute value that can be various types
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(untagged)]
pub enum AttributeValue {
    String(String),
    Integer(i64),
    Boolean(bool),
    Binary(#[serde(with = "serde_bytes")] Vec<u8>),
    JID(String), // For now, we'll represent JIDs as strings
}

/// Content of a binary node
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(untagged)]
pub enum NodeContent {
    None,
    Text(String),
    Binary(#[serde(with = "serde_bytes")] Vec<u8>),
    Children(Vec<Node>),
}

impl AttributeValue {
    /// Convert to string representation
    pub fn as_string(&self) -> String {
        match self {
            AttributeValue::String(s) => s.clone(),
            AttributeValue::Integer(i) => i.to_string(),
            AttributeValue::Boolean(b) => b.to_string(),
            AttributeValue::Binary(data) => {
                use base64::{engine::general_purpose, Engine as _};
                general_purpose::STANDARD.encode(data)
            }
            AttributeValue::JID(jid) => jid.clone(),
        }
    }

    /// Check if this is a string value
    pub fn is_string(&self) -> bool {
        matches!(self, AttributeValue::String(_))
    }

    /// Get as string reference if it's a string
    pub fn as_str(&self) -> Option<&str> {
        match self {
            AttributeValue::String(s) => Some(s),
            AttributeValue::JID(jid) => Some(jid),
            _ => None,
        }
    }

    /// Get as integer if it's an integer
    pub fn as_i64(&self) -> Option<i64> {
        match self {
            AttributeValue::Integer(i) => Some(*i),
            _ => None,
        }
    }

    /// Get as boolean if it's a boolean
    pub fn as_bool(&self) -> Option<bool> {
        match self {
            AttributeValue::Boolean(b) => Some(*b),
            _ => None,
        }
    }
}

impl From<String> for AttributeValue {
    fn from(s: String) -> Self {
        AttributeValue::String(s)
    }
}

impl From<&str> for AttributeValue {
    fn from(s: &str) -> Self {
        AttributeValue::String(s.to_string())
    }
}

impl From<i64> for AttributeValue {
    fn from(i: i64) -> Self {
        AttributeValue::Integer(i)
    }
}

impl From<bool> for AttributeValue {
    fn from(b: bool) -> Self {
        AttributeValue::Boolean(b)
    }
}

impl Node {
    /// Create a new node with tag
    pub fn new<S: Into<String>>(tag: S) -> Self {
        Self {
            tag: tag.into(),
            attributes: HashMap::new(),
            content: NodeContent::None,
        }
    }

    /// Create a node with text content
    pub fn with_text<S: Into<String>, T: Into<String>>(tag: S, text: T) -> Self {
        Self {
            tag: tag.into(),
            attributes: HashMap::new(),
            content: NodeContent::Text(text.into()),
        }
    }

    /// Create a node with binary content
    pub fn with_binary<S: Into<String>>(tag: S, data: Vec<u8>) -> Self {
        Self {
            tag: tag.into(),
            attributes: HashMap::new(),
            content: NodeContent::Binary(data),
        }
    }

    /// Create a node with children
    pub fn with_children<S: Into<String>>(tag: S, children: Vec<Node>) -> Self {
        Self {
            tag: tag.into(),
            attributes: HashMap::new(),
            content: NodeContent::Children(children),
        }
    }

    /// Add an attribute to the node
    pub fn with_attribute<K: Into<String>, V: Into<AttributeValue>>(
        mut self,
        key: K,
        value: V,
    ) -> Self {
        self.attributes.insert(key.into(), value.into());
        self
    }

    /// Set an attribute on the node
    pub fn set_attribute<K: Into<String>, V: Into<AttributeValue>>(&mut self, key: K, value: V) {
        self.attributes.insert(key.into(), value.into());
    }

    /// Get an attribute value
    pub fn get_attribute(&self, key: &str) -> Option<&AttributeValue> {
        self.attributes.get(key)
    }

    /// Get an attribute as string
    pub fn get_attribute_str(&self, key: &str) -> Option<&str> {
        self.attributes.get(key).and_then(|v| v.as_str())
    }

    /// Get an attribute as integer
    pub fn get_attribute_i64(&self, key: &str) -> Option<i64> {
        self.attributes.get(key).and_then(|v| v.as_i64())
    }

    /// Get an attribute as boolean
    pub fn get_attribute_bool(&self, key: &str) -> Option<bool> {
        self.attributes.get(key).and_then(|v| v.as_bool())
    }

    /// Check if node has an attribute
    pub fn has_attribute(&self, key: &str) -> bool {
        self.attributes.contains_key(key)
    }

    /// Remove an attribute
    pub fn remove_attribute(&mut self, key: &str) -> Option<AttributeValue> {
        self.attributes.remove(key)
    }

    /// Get text content if available
    pub fn text(&self) -> Option<&String> {
        match &self.content {
            NodeContent::Text(text) => Some(text),
            _ => None,
        }
    }

    /// Get binary content if available
    pub fn binary(&self) -> Option<&Vec<u8>> {
        match &self.content {
            NodeContent::Binary(data) => Some(data),
            _ => None,
        }
    }

    /// Get children if available
    pub fn children(&self) -> Option<&Vec<Node>> {
        match &self.content {
            NodeContent::Children(children) => Some(children),
            _ => None,
        }
    }

    /// Get mutable children if available
    pub fn children_mut(&mut self) -> Option<&mut Vec<Node>> {
        match &mut self.content {
            NodeContent::Children(children) => Some(children),
            _ => None,
        }
    }

    /// Get children by tag
    pub fn get_children_by_tag(&self, tag: &str) -> Vec<&Node> {
        match &self.content {
            NodeContent::Children(children) => {
                children.iter().filter(|child| child.tag == tag).collect()
            }
            _ => Vec::new(),
        }
    }

    /// Get first child by tag
    pub fn get_child_by_tag(&self, tag: &str) -> Option<&Node> {
        match &self.content {
            NodeContent::Children(children) => children.iter().find(|child| child.tag == tag),
            _ => None,
        }
    }

    /// Get nested child by tag path
    pub fn get_nested_child(&self, tags: &[&str]) -> Option<&Node> {
        let mut current = self;
        for &tag in tags {
            current = current.get_child_by_tag(tag)?;
        }
        Some(current)
    }

    /// Add a child node
    pub fn add_child(&mut self, child: Node) {
        match &mut self.content {
            NodeContent::Children(children) => {
                children.push(child);
            }
            NodeContent::None => {
                self.content = NodeContent::Children(vec![child]);
            }
            _ => {
                // Convert existing content to children with new child
                let _old_content = std::mem::replace(&mut self.content, NodeContent::None);
                self.content = NodeContent::Children(vec![child]);
            }
        }
    }

    /// Set content to text
    pub fn set_text<S: Into<String>>(&mut self, text: S) {
        self.content = NodeContent::Text(text.into());
    }

    /// Set content to binary
    pub fn set_binary(&mut self, data: Vec<u8>) {
        self.content = NodeContent::Binary(data);
    }

    /// Set content to children
    pub fn set_children(&mut self, children: Vec<Node>) {
        self.content = NodeContent::Children(children);
    }

    /// Check if node has content
    pub fn has_content(&self) -> bool {
        !matches!(self.content, NodeContent::None)
    }

    /// Check if node is empty (no attributes and no content)
    pub fn is_empty(&self) -> bool {
        self.attributes.is_empty() && matches!(self.content, NodeContent::None)
    }

    /// Get the number of children
    pub fn child_count(&self) -> usize {
        match &self.content {
            NodeContent::Children(children) => children.len(),
            _ => 0,
        }
    }

    /// Parse a binary node from bytes using the parser
    pub fn parse(data: &[u8]) -> Result<Self, BinaryError> {
        use crate::parser::Parser;

        let mut parser = Parser::new(Bytes::copy_from_slice(data));

        parser.parse_node()
    }

    /// Serialize the node to bytes using the serializer
    pub fn serialize(&self) -> Result<Vec<u8>, BinaryError> {
        use crate::serializer::Serializer;
        let mut serializer = Serializer::new();
        serializer.serialize_node(self)?;
        Ok(serializer.into_bytes())
    }
}
